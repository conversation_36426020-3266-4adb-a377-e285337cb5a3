<?php

namespace Theme25\Helper;

use GlobalRegistry;

/**
 * Shipping Helper
 * Помощен клас за работа с доставки
 * Следва конвенциите на Rakla.bg архитектурата
 */
class ShippingHelper {

    private static $db = null;
    protected static $modelInstance = null;
    protected static $registry = null;

    public static $fieldRules = [];

    public function __construct($registry) {
        self::$db = $registry->get('db');
        // self::$modelInstance = new \Theme25\Model($registry);
        self::$registry = $registry;

        F()->log->developer('>>> ShippingHelper constructor', __FILE__, __LINE__);
    }
    

    public static function getInstance($registry=null) {
        static $instance;
        if (!$instance) {
            if($registry == null) $registry = \GlobalRegistry::$registry;
            $instance = new self($registry);
        }
        return $instance;
    } 

    public function getActiveShippingMethods() {
        $deliveryModel = CM(self::$registry)->loadModelAs('setting/delivery', 'deliveryModel');


        F()->log->developer(gettype($deliveryModel), __FILE__, __LINE__);
        F()->log->developer('>>> getActiveShippingMethods from ShippingHelper', __FILE__, __LINE__);

        $results = $deliveryModel->getDeliveryMethods();

        $active_methods = [];

        foreach ($results as $result) {
            if ($result['status'] == 1) {
                $active_methods[] = $result['code'];
            }
        }

        return $active_methods;
    }

    public static function db() {
        if(self::$db == null && \GlobalRegistry::$registry) {
            self::$db = \GlobalRegistry::$registry->get('db');
        }
        return self::$db;
    }

    public static function __callStatic($method, $args) {
        // if(self::$modelInstance == null) {
        //     self::$modelInstance = new \Theme25\Model(\GlobalRegistry::$registry);

        //     F()->log->developer('>>> init ShippingHelper __callStatic', __FILE__, __LINE__);
        // }


        if(is_callable([self::$modelInstance, $method])) {
            return call_user_func_array([self::$modelInstance, $method], $args);
        }
    }

    
}
