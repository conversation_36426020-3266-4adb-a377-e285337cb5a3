/**
 * JavaScript модул за редактиране на поръчка
 * Разширява основния BackendModule клас
 */
(function() {
    'use strict';

    // Разширяване на основния модул
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализиране на функционалността за редактиране на поръчка
        if (typeof BackendModule !== 'undefined') {
            BackendModule.initOrderEditModule();
        } else {
            console.error('ORDER-EDIT.JS: BackendModule is not defined!');
        }
    });

    // Добавяне на функционалност за редактиране на поръчка към основния модул
    if (typeof BackendModule !== 'undefined') {
        // Ensure config object exists
        BackendModule.config = BackendModule.config || {};

        // Extract user_token from URL and store it
        try {
            const params = new URLSearchParams(window.location.search);
            BackendModule.config.userToken = params.get('user_token') || '';
        } catch (e) {
            console.error('Error parsing URL params for user_token:', e);
            BackendModule.config.userToken = '';
        }

        // Добавяне на методи към основния модул
        Object.assign(BackendModule, {
            /**
             * Инициализация на функционалността за редактиране на поръчка
             */
            // Debounce и флаг за активна заявка
            debounceTimer: null,
            isCalculating: false,

            initOrderEditModule: function() {
                this.initOrderEditForm();
                this.initOrderValidation();
                this.initAddressHandling();
                this.initProductManagement();
                this.initOrderTotalsCalculation();
                this.initDiscountFunctionality();
            },

            /**
             * Инициализация на формата за редактиране на поръчка
             */
            initOrderEditForm: function() {
                const orderForm = document.getElementById('order-edit-form');

                if (orderForm) {
                    orderForm.addEventListener('submit', function(e) {
                        e.preventDefault();

                        // Валидация на формата
                        if (!BackendModule.validateOrderForm(this)) {
                            return;
                        }

                        const formData = new FormData(this);

                        // Показване на loading индикатор
                        BackendModule.showLoadingIndicator();

                        const actionUrl = this.action + '&user_token=' + BackendModule.config.userToken;

                        fetch(actionUrl, {
                            method: 'POST',
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest'
                            },
                            body: formData
                        })
                        .then(response => response.json())
                        .then(data => {
                            BackendModule.hideLoadingIndicator();

                            if (data.success) {
                                BackendModule.showOrderAlert('success', data.success);
                                // Пренасочване към страницата с информация за поръчката
                                // setTimeout(() => {
                                //     const orderId = document.querySelector('[name="order_id"]').value;
                                //     const infoUrl = window.location.origin + window.location.pathname +
                                //                   '?route=sale/order/info&order_id=' + orderId +
                                //                   '&user_token=' + BackendModule.config.userToken;
                                //     window.location.href = infoUrl;
                                // }, 1500);
                            } else if (data.error) {
                                BackendModule.showOrderAlert('error', data.error);
                            }
                        })
                        .catch(error => {
                            BackendModule.hideLoadingIndicator();
                            console.error('Error:', error);
                            BackendModule.showOrderAlert('error', 'Възникна грешка при запазване на поръчката');
                        });
                    });
                }
            },

            /**
             * Инициализация на валидацията на формата
             */
            initOrderValidation: function() {
                // Валидация в реalno време
                const requiredFields = document.querySelectorAll('[required]');

                requiredFields.forEach(field => {
                    field.addEventListener('blur', function() {
                        BackendModule.validateField(this);
                    });

                    field.addEventListener('input', function() {
                        // Премахване на грешката при въвеждане
                        this.classList.remove('border-red-500');
                        const errorMsg = this.parentNode.querySelector('.error-message');
                        if (errorMsg) {
                            errorMsg.remove();
                        }
                    });
                });

                // Валидация на имейл полета
                const emailFields = document.querySelectorAll('input[type="email"]');
                emailFields.forEach(field => {
                    field.addEventListener('blur', function() {
                        BackendModule.validateEmailField(this);
                    });
                });
            },

            /**
             * Валидация на цялата форма
             */
            validateOrderForm: function(form) {
                let isValid = true;
                const requiredFields = form.querySelectorAll('[required]');

                // Изчистване на предишни грешки
                form.querySelectorAll('.error-message').forEach(msg => msg.remove());
                form.querySelectorAll('.border-red-500').forEach(field => {
                    field.classList.remove('border-red-500');
                });

                requiredFields.forEach(field => {
                    if (!BackendModule.validateField(field)) {
                        isValid = false;
                    }
                });

                // Валидация на имейл полета
                const emailFields = form.querySelectorAll('input[type="email"]');
                emailFields.forEach(field => {
                    if (!BackendModule.validateEmailField(field)) {
                        isValid = false;
                    }
                });

                return isValid;
            },

            /**
             * Валидация на отделно поле
             */
            validateField: function(field) {
                const value = field.value.trim();
                let isValid = true;
                let errorMessage = '';

                if (field.hasAttribute('required') && !value) {
                    isValid = false;
                    errorMessage = 'Това поле е задължително';
                }

                if (!isValid) {
                    field.classList.add('border-red-500');
                    BackendModule.showFieldError(field, errorMessage);
                } else {
                    field.classList.remove('border-red-500');
                    BackendModule.hideFieldError(field);
                }

                return isValid;
            },

            /**
             * Валидация на имейл поле
             */
            validateEmailField: function(field) {
                const value = field.value.trim();
                let isValid = true;
                let errorMessage = '';

                if (value && !BackendModule.isValidEmail(value)) {
                    isValid = false;
                    errorMessage = 'Моля, въведете валиден имейл адрес';
                }

                if (!isValid) {
                    field.classList.add('border-red-500');
                    BackendModule.showFieldError(field, errorMessage);
                } else {
                    field.classList.remove('border-red-500');
                    BackendModule.hideFieldError(field);
                }

                return isValid;
            },

            /**
             * Проверка за валиден имейл
             */
            isValidEmail: function(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            },

            /**
             * Показване на грешка за поле
             */
            showFieldError: function(field, message) {
                // Премахване на съществуваща грешка
                this.hideFieldError(field);

                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message text-red-500 text-sm mt-1';
                errorDiv.textContent = message;

                field.parentNode.appendChild(errorDiv);
            },

            /**
             * Скриване на грешка за поле
             */
            hideFieldError: function(field) {
                const errorMsg = field.parentNode.querySelector('.error-message');
                if (errorMsg) {
                    errorMsg.remove();
                }
            },

            /**
             * Инициализация на работата с адреси
             */
            initAddressHandling: function() {
                // Копиране на адрес за плащане към адрес за доставка
                const copyAddressBtn = document.getElementById('copy-billing-address');

                if (copyAddressBtn) {
                    copyAddressBtn.addEventListener('click', function() {
                        BackendModule.copyBillingToShipping();
                    });
                }

                // Автоматично попълване на адреси при избор на клиент
                const customerSelect = document.getElementById('customer-select');
                if (customerSelect) {
                    customerSelect.addEventListener('change', function() {
                        const customerId = this.value;
                        if (customerId) {
                            BackendModule.loadCustomerAddresses(customerId);
                        }
                    });
                }
            },

            /**
             * Копиране на адрес за плащане към адрес за доставка
             */
            copyBillingToShipping: function() {
                const billingFields = [
                    'payment_firstname', 'payment_lastname', 'payment_company',
                    'payment_address_1', 'payment_address_2', 'payment_city',
                    'payment_postcode', 'payment_country_id', 'payment_zone_id'
                ];

                const shippingFields = [
                    'shipping_firstname', 'shipping_lastname', 'shipping_company',
                    'shipping_address_1', 'shipping_address_2', 'shipping_city',
                    'shipping_postcode', 'shipping_country_id', 'shipping_zone_id'
                ];

                billingFields.forEach((billingField, index) => {
                    const billingInput = document.querySelector(`[name="${billingField}"]`);
                    const shippingInput = document.querySelector(`[name="${shippingFields[index]}"]`);

                    if (billingInput && shippingInput) {
                        shippingInput.value = billingInput.value;
                    }
                });

                BackendModule.showOrderAlert('success', 'Адресът за плащане е копиран към адреса за доставка');
            },

            /**
             * Зареждане на адресите на клиент
             */
            loadCustomerAddresses: function(customerId) {
                const formData = new FormData();
                formData.append('customer_id', customerId);

                const addressUrl = window.location.origin + window.location.pathname + '?route=sale/order/getCustomerAddresses&user_token=' + BackendModule.config.userToken;

                fetch(addressUrl, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.addresses) {
                        BackendModule.populateAddressFields(data.addresses);
                    }
                })
                .catch(error => {
                    console.error('Error loading customer addresses:', error);
                });
            },

            /**
             * Попълване на полетата за адреси
             */
            populateAddressFields: function(addresses) {
                if (addresses.billing) {
                    Object.keys(addresses.billing).forEach(key => {
                        const field = document.querySelector(`[name="payment_${key}"]`);
                        if (field) {
                            field.value = addresses.billing[key];
                        }
                    });
                }

                if (addresses.shipping) {
                    Object.keys(addresses.shipping).forEach(key => {
                        const field = document.querySelector(`[name="shipping_${key}"]`);
                        if (field) {
                            field.value = addresses.shipping[key];
                        }
                    });
                }
            },

            /**
             * Показване на loading индикатор
             */
            showLoadingIndicator: function() {
                const submitBtn = document.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Запазване...';
                }
            },

            /**
             * Скриване на loading индикатор
             */
            hideLoadingIndicator: function() {
                const submitBtn = document.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="ri-save-line mr-2"></i>Запази промените';
                }
            },

            /**
             * Инициализация на управлението на продукти
             */
            initProductManagement: function() {
                // Добавяне на нов продукт
                const addProductBtn = document.getElementById('add-product-btn');
                if (addProductBtn) {
                    addProductBtn.addEventListener('click', function() {
                        BackendModule.showAddProductModal();
                    });
                }

                // Премахване на продукт
                const removeProductBtns = document.querySelectorAll('.remove-product-btn');
                removeProductBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        const productRow = this.closest('tr');
                        if (productRow && confirm('Сигурни ли сте, че искате да премахнете този продукт?')) {
                            const productId = this.getAttribute('data-product-id');
                            // AJAX заявка за премахване от сесията
                            BackendModule.removeProductFromSession(productId, productRow);
                        }
                    });
                });

                // Промяна на количество
                const quantityInputs = document.querySelectorAll('.product-quantity');
                quantityInputs.forEach(input => {
                    input.addEventListener('change', function() {
                        BackendModule.updateProductTotal(this);
                        BackendModule.handleProductQuantityChange(this);
                    });

                    input.addEventListener('input', function() {
                        BackendModule.updateProductTotal(this);
                        BackendModule.scheduleTotalsRecalculation();
                    });
                });

                // Промяна на цена
                const priceInputs = document.querySelectorAll('.product-price');
                priceInputs.forEach(input => {
                    input.addEventListener('change', function() {
                        BackendModule.updateProductTotal(this);
                        BackendModule.handleProductPriceChange(this);
                    });

                    input.addEventListener('input', function() {
                        BackendModule.updateProductTotal(this);
                        BackendModule.scheduleTotalsRecalculation();
                    });
                });

                // Премахнато автоматичното преизчисляване при зареждане на страницата
                // Изчисляването се стартира само при реални промени от потребителя
            },

            /**
             * Показване на модал за добавяне на продукт
             */
            showAddProductModal: function() {
                // Създаване на модален прозорец
                const modal = document.createElement('div');
                modal.id = 'add-product-modal';
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
                modal.innerHTML = `
                    <div class="bg-white rounded-lg w-full max-w-lg mx-4">
                        <div class="flex justify-between items-center p-6 border-b border-gray-200">
                            <h3 class="text-lg font-semibold text-gray-800">Добавяне на продукт</h3>
                            <button class="close-modal text-gray-400 hover:text-gray-500">
                                <i class="ri-close-line text-xl"></i>
                            </button>
                        </div>
                        <div class="p-6">
                            <form id="add-product-form">
                                <div class="mb-4 relative">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Продукт</label>
                                    <input type="text" id="product-search" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" placeholder="Търсене на продукт..." autocomplete="off">
                                    <div id="product-suggestions" class="hidden absolute z-10 w-full bg-white border border-gray-300 rounded-button mt-1 max-h-48 overflow-y-auto shadow-lg"></div>
                                    <input type="hidden" id="selected-product-id" name="product_id" value="">
                                </div>
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Количество</label>
                                    <input type="number" name="quantity" min="1" value="1" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" required>
                                </div>
                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Цена</label>
                                    <input type="number" name="price" step="0.01" min="0" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" required>
                                </div>
                                <div id="product-options-container" class="mb-4 hidden">
                                    <h4 class="text-sm font-medium text-gray-700 mb-3">Опции на продукта</h4>
                                    <div id="product-options-list"></div>
                                </div>
                                <div class="flex justify-end space-x-2">
                                    <button type="button" class="close-modal px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50">Отказ</button>
                                    <button type="submit" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90">Добави продукт</button>
                                </div>
                            </form>
                        </div>
                    </div>
                `;

                // Добавяне на event listeners
                modal.addEventListener('click', (e) => {
                    // Проверка дали кликът е върху фона на модала
                    if (e.target === modal) {
                        document.body.removeChild(modal);
                        document.body.style.overflow = 'auto';
                    }

                    // Проверка дали кликът е върху бутон за затваряне или негово дете
                    const closeButton = e.target.closest('.close-modal');
                    if (closeButton) {
                        document.body.removeChild(modal);
                        document.body.style.overflow = 'auto';
                    }
                });

                // Обработка на формата
                modal.querySelector('#add-product-form').addEventListener('submit', (e) => {
                    e.preventDefault();
                    BackendModule.addProductToOrder(new FormData(e.target));
                    document.body.removeChild(modal);
                    document.body.style.overflow = 'auto';
                });

                // Търсене на продукти
                const productSearch = modal.querySelector('#product-search');
                productSearch.addEventListener('input', function() {
                    const query = this.value.trim();
                    if (query.length === 0) {
                        // Показване на първите 10 продукта при празно поле
                        BackendModule.loadInitialProducts();
                    } else if (query.length >= 2) {
                        BackendModule.searchProducts(query, false);
                    } else {
                        document.getElementById('product-suggestions').classList.add('hidden');
                    }
                });

                // Показване на първите продукти при фокус
                productSearch.addEventListener('focus', function() {
                    if (this.value.trim().length === 0) {
                        BackendModule.loadInitialProducts();
                    } else if (this.value.trim().length >= 2) {
                        BackendModule.searchProducts(this.value.trim(), false);
                    }
                });

                // Скриване на предложенията при загуба на фокус (с малко забавяне за кликване)
                productSearch.addEventListener('blur', function() {
                    setTimeout(() => {
                        document.getElementById('product-suggestions').classList.add('hidden');
                    }, 200);
                });

                document.body.appendChild(modal);
                document.body.style.overflow = 'hidden';
            },

            /**
             * Зареждане на първоначални продукти
             */
            loadInitialProducts: function() {
                // Нулиране на pagination данните
                BackendModule.productSearch = {
                    currentPage: 0,
                    hasMore: true,
                    isLoading: false,
                    query: ''
                };

                const formData = new FormData();
                formData.append('limit', '10');
                formData.append('start', '0');

                const searchUrl = window.location.origin + window.location.pathname + '?route=sale/order/searchProducts&user_token=' + BackendModule.config.userToken;

                BackendModule.productSearch.isLoading = true;
                BackendModule.showProductLoadingIndicator();

                fetch(searchUrl, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    BackendModule.productSearch.hasMore = data.has_more || false;
                    BackendModule.productSearch.currentPage = 1;
                    BackendModule.displayProductSuggestions(data.products || [], true);
                    BackendModule.hideProductLoadingIndicator();
                    BackendModule.productSearch.isLoading = false;
                })
                .catch(error => {
                    console.error('Error loading initial products:', error);
                    BackendModule.hideProductLoadingIndicator();
                    BackendModule.productSearch.isLoading = false;
                });
            },

            /**
             * Търсене на продукти
             */
            searchProducts: function(query, append = false) {
                // Ако не е append заявка, нулираме pagination данните
                if (!append) {
                    BackendModule.productSearch = {
                        currentPage: 0,
                        hasMore: true,
                        isLoading: false,
                        query: query
                    };
                }

                if (BackendModule.productSearch.isLoading) {
                    return;
                }

                const start = append ? BackendModule.productSearch.currentPage * 10 : 0;

                const formData = new FormData();
                formData.append('search', query);
                formData.append('limit', '10');
                formData.append('start', start.toString());

                const searchUrl = window.location.origin + window.location.pathname + '?route=sale/order/searchProducts&user_token=' + BackendModule.config.userToken;

                BackendModule.productSearch.isLoading = true;
                if (append) {
                    BackendModule.showProductLoadingIndicator();
                }

                fetch(searchUrl, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    BackendModule.productSearch.hasMore = data.has_more || false;
                    BackendModule.productSearch.currentPage++;
                    BackendModule.displayProductSuggestions(data.products || [], !append);
                    BackendModule.hideProductLoadingIndicator();
                    BackendModule.productSearch.isLoading = false;
                })
                .catch(error => {
                    console.error('Error searching products:', error);
                    BackendModule.hideProductLoadingIndicator();
                    BackendModule.productSearch.isLoading = false;
                });
            },

            /**
             * Показване на предложения за продукти
             */
            displayProductSuggestions: function(products, clearFirst = true) {
                const suggestionsDiv = document.getElementById('product-suggestions');

                if (clearFirst) {
                    suggestionsDiv.innerHTML = '';
                }

                if (products.length === 0 && clearFirst) {
                    suggestionsDiv.innerHTML = '<div class="p-2 text-gray-500 text-sm">Няма намерени продукти</div>';
                    suggestionsDiv.classList.remove('hidden');
                    return;
                }

                products.forEach(product => {
                    const suggestion = document.createElement('div');
                    suggestion.className = 'p-3 hover:bg-gray-100 cursor-pointer border-b border-gray-200 last:border-b-0';

                    const imageHtml = product.image ?
                        `<img class="w-10 h-10 rounded object-cover object-top mr-3" src="${product.image}" alt="${product.name}">` :
                        '<div class="w-10 h-10 bg-gray-200 rounded mr-3 flex items-center justify-center"><i class="ri-image-line text-gray-400"></i></div>';

                    suggestion.innerHTML = `
                        <div class="flex items-center">
                            ${imageHtml}
                            <div class="flex-1">
                                <div class="font-medium text-sm">${product.name}</div>
                                <div class="text-xs text-gray-500">Модел: ${product.model || 'N/A'} | Цена: ${product.price} лв.</div>
                            </div>
                        </div>
                    `;

                    suggestion.addEventListener('click', function() {
                        document.getElementById('product-search').value = product.name;

                        const priceInput = document.querySelector('[name="price"]');
                        const productPrice = parseFloat(product.price).toFixed(2);
                        priceInput.value = productPrice;

                        // Запазване на оригиналната цена на продукта
                        priceInput.setAttribute('data-original-price', productPrice);

                        document.getElementById('selected-product-id').value = product.product_id;

                        // Запазване на данните за продукта за по-късно използване
                        document.getElementById('selected-product-id').setAttribute('data-product-name', product.name);
                        document.getElementById('selected-product-id').setAttribute('data-product-model', product.model || '');
                        document.getElementById('selected-product-id').setAttribute('data-product-image', product.image_large || product.image || '');

                        // Зареждане на опциите за избрания продукт
                        BackendModule.loadProductOptions(product.product_id);

                        suggestionsDiv.classList.add('hidden');
                    });

                    suggestionsDiv.appendChild(suggestion);
                });

                // Добавяне на scroll listener за infinite scroll
                if (clearFirst) {
                    BackendModule.setupInfiniteScroll();
                }

                suggestionsDiv.classList.remove('hidden');
            },

            /**
             * Добавяне на продукт към поръчката
             */
            addProductToOrder: function(formData) {
                const productName = document.getElementById('product-search').value;
                const quantity = formData.get('quantity');
                const price = formData.get('price');
                const productId = document.getElementById('selected-product-id').value || 0;

                // Получаване на запазените данни за продукта
                const selectedProductElement = document.getElementById('selected-product-id');
                const productModel = selectedProductElement.getAttribute('data-product-model') || '';
                const productImage = selectedProductElement.getAttribute('data-product-image') || '';

                if (!productName || !quantity || !price || !productId) {
                    BackendModule.showOrderAlert('error', 'Моля, изберете продукт и попълнете всички полета');
                    return;
                }

                // Получаване на order_id
                const orderIdInput = document.querySelector('input[name="order_id"]');
                const orderId = orderIdInput ? orderIdInput.value : null;

                if (!orderId) {
                    BackendModule.showOrderAlert('error', 'Не може да се намери ID на поръчката');
                    return;
                }

                // Събиране на избраните опции
                const selectedOptions = BackendModule.getSelectedProductOptions();

                // AJAX заявка към addProductToSessionAjax за добавяне в OrderSession
                const ajaxFormData = new FormData();
                ajaxFormData.append('order_id', orderId);
                ajaxFormData.append('product_id', productId);
                ajaxFormData.append('quantity', quantity);
                ajaxFormData.append('price', price);
                ajaxFormData.append('name', productName);
                ajaxFormData.append('model', productModel);

                // Добавяне на опциите към AJAX заявката
                selectedOptions.forEach((option, index) => {
                    ajaxFormData.append(`options[${index}][product_option_id]`, option.product_option_id || '');
                    ajaxFormData.append(`options[${index}][product_option_value_id]`, option.product_option_value_id || '');
                    ajaxFormData.append(`options[${index}][name]`, option.name);
                    ajaxFormData.append(`options[${index}][value]`, option.value);
                    if (option.option_value_id) {
                        ajaxFormData.append(`options[${index}][option_value_id]`, option.option_value_id);
                    }
                });

                // Показване на loading индикатор
                BackendModule.showLoadingIndicator();

                fetch(BackendModule.getAdminUrl('sale/order/edit/addProductToSessionAjax'), {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: ajaxFormData
                })
                .then(response => response.json())
                .then(data => {
                    BackendModule.hideLoadingIndicator();

                    if (data.success) {
                        // Добавяне на ред в таблицата с продукти
                        const productsTable = document.getElementById('order-products-table');
                        const tbody = productsTable.querySelector('tbody');

                        // Генериране на композитен ID за новия продукт
                        const compositeId = BackendModule.generateCompositeProductId(productId, selectedOptions);
                        const total = (parseFloat(quantity) * parseFloat(price)).toFixed(2);

                        // Подготовка на HTML за изображението
                        let imageHtml = '';
                        if (productImage) {
                            imageHtml = `<img class="h-16 w-16 rounded object-cover object-top" src="${productImage}" alt="${productName}">`;
                        } else {
                            imageHtml = '<i class="ri-image-line text-gray-400"></i>';
                        }

                        // Подготовка на HTML за опциите
                        let optionsHtml = '';
                        if (selectedOptions.length > 0) {
                            selectedOptions.forEach(option => {
                                optionsHtml += `<div class="text-xs text-gray-500">${option.name}: ${option.value}</div>`;
                            });
                        }

                        const row = document.createElement('tr');
                        row.setAttribute('data-product-row', compositeId);
                        row.innerHTML = `
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-16 w-16 bg-gray-100 rounded">
                                        ${imageHtml}
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">${productName}</div>
                                        ${productModel ? `<div class="text-sm text-gray-500">Модел: ${productModel}</div>` : ''}
                                        ${optionsHtml}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-center">
                                <input type="number" name="products[${compositeId}][quantity]" value="${quantity}" min="1" class="product-quantity w-20 px-2 py-1 border border-gray-300 rounded text-center text-sm" data-product-id="${compositeId}" data-is-new="true">
                            </td>
                            <td class="px-6 py-4 text-right">
                                <input type="number" name="products[${compositeId}][price]" value="${parseFloat(price).toFixed(2)}" step="0.01" class="product-price w-24 px-2 py-1 border border-gray-300 rounded text-right text-sm" data-product-id="${compositeId}" data-is-new="true">
                            </td>
                            <td class="px-6 py-4 text-right text-sm font-medium text-gray-900 product-total" data-product-id="${compositeId}">
                                ${total}
                            </td>
                            <td class="px-6 py-4 text-center">
                                <button type="button" class="remove-product-btn text-red-500 hover:text-red-700 transition-colors" data-product-id="${compositeId}" data-is-new="true">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-delete-bin-line"></i>
                                    </div>
                                </button>

                                <!-- Hidden fields for product data -->
                                <input type="hidden" name="products[${compositeId}][product_id]" value="${productId}">
                                <input type="hidden" name="products[${compositeId}][name]" value="${productName}">
                                ${productModel ? `<input type="hidden" name="products[${compositeId}][model]" value="${productModel}">` : ''}

                                <!-- Hidden fields for product options -->
                                ${BackendModule.generateOptionsHiddenFields(compositeId, selectedOptions)}
                            </td>
                        `;

                        tbody.appendChild(row);

                        // Добавяне на event listeners за новия ред
                        const quantityInput = row.querySelector('.product-quantity');
                        const priceInput = row.querySelector('.product-price');
                        const removeBtn = row.querySelector('.remove-product-btn');

                        quantityInput.addEventListener('change', function() {
                            BackendModule.updateProductTotal(this);
                            BackendModule.handleProductQuantityChange(this);
                        });

                        quantityInput.addEventListener('input', function() {
                            BackendModule.updateProductTotal(this);
                            BackendModule.scheduleTotalsRecalculation();
                        });

                        priceInput.addEventListener('change', function() {
                            BackendModule.updateProductTotal(this);
                            BackendModule.handleProductPriceChange(this);
                        });

                        priceInput.addEventListener('input', function() {
                            BackendModule.updateProductTotal(this);
                            BackendModule.scheduleTotalsRecalculation();
                        });

                        removeBtn.addEventListener('click', function() {
                            if (confirm('Сигурни ли сте, че искате да премахнете този продукт?')) {
                                // AJAX заявка за премахване от сесията
                                BackendModule.removeProductFromSession(compositeId, row);
                            }
                        });

                        // Актуализиране на тоталите с данните от сървъра
                        if (data.totals) {
                            BackendModule.updateTotalsDisplay(data.totals);
                        }

                        BackendModule.showOrderAlert('success', data.message || 'Продуктът е добавен успешно');
                    } else if (data.error) {
                        BackendModule.showOrderAlert('error', data.error);
                    }
                })
                .catch(error => {
                    BackendModule.hideLoadingIndicator();
                    console.error('Error adding product:', error);
                    BackendModule.showOrderAlert('error', 'Възникна грешка при добавяне на продукта');
                });
            },

            /**
             * Премахване на продукт от сесията чрез AJAX
             */
            removeProductFromSession: function(productId, row) {
                // Получаване на order_id
                const orderIdInput = document.querySelector('input[name="order_id"]');
                const orderId = orderIdInput ? orderIdInput.value : null;

                if (!orderId) {
                    BackendModule.showOrderAlert('error', 'Не може да се намери ID на поръчката');
                    return;
                }

                // AJAX заявка към removeProductFromSessionAjax за всички продукти
                // OrderSession ще се справи с разпознаването на типа продукт
                const ajaxFormData = new FormData();
                ajaxFormData.append('order_id', orderId);
                ajaxFormData.append('order_product_id', productId);

                // Показване на loading индикатор
                BackendModule.showLoadingIndicator();

                fetch(BackendModule.getAdminUrl('sale/order/edit/removeProductFromSessionAjax'), {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: ajaxFormData
                })
                .then(response => response.json())
                .then(data => {
                    BackendModule.hideLoadingIndicator();

                    if (data.success) {
                        // Премахване на реда от таблицата
                        row.remove();

                        // Проверка дали има останали продукти
                        const remainingRows = document.querySelectorAll('#order-products-table tbody tr');
                        if (remainingRows.length === 0) {
                            BackendModule.resetTotalsUI();
                        } else {
                            // Актуализиране на тоталите с данните от сървъра
                            if (data.totals) {
                                BackendModule.updateTotalsDisplay(data.totals);
                            }
                        }

                        BackendModule.showOrderAlert('success', data.message || 'Продуктът е премахнат успешно');
                    } else if (data.error) {
                        BackendModule.showOrderAlert('error', data.error);
                    }
                })
                .catch(error => {
                    BackendModule.hideLoadingIndicator();
                    console.error('Error removing product:', error);
                    BackendModule.showOrderAlert('error', 'Възникна грешка при премахване на продукта');
                });
            },

            /**
             * Проверява дали product_id е композитен (нов продукт) или реален (съществуващ)
             *
             * @param {string} productId - ID на продукта за проверка
             * @returns {boolean} - true ако е композитен, false ако е реален
             */
            isCompositeProductId: function(productId) {
                // Композитните ID-та съдържат подчертавки или са по-дълги от обичайните числови ID-та
                return !(/^\d+$/.test(productId)) || productId.indexOf('_') !== -1;
            },

            /**
             * Обработка на промяна в количество на продукт
             */
            handleProductQuantityChange: function(input) {
                const productId = input.getAttribute('data-product-id');

                // Всички продукти в OrderSession се обработват еднакво
                BackendModule.updateProductQuantityInSession(productId, input.value);
            },

            /**
             * Обработка на промяна в цена на продукт
             */
            handleProductPriceChange: function(input) {
                const productId = input.getAttribute('data-product-id');

                // Всички продукти в OrderSession се обработват еднакво
                BackendModule.updateProductPriceInSession(productId, input.value);
            },

            /**
             * Актуализиране на количество в OrderSession чрез AJAX
             */
            updateProductQuantityInSession: function(productId, quantity) {
                // Получаване на order_id
                const orderIdInput = document.querySelector('input[name="order_id"]');
                const orderId = orderIdInput ? orderIdInput.value : null;

                if (!orderId) {
                    console.error('Не може да се намери ID на поръчката');
                    return;
                }

                const ajaxFormData = new FormData();
                ajaxFormData.append('order_id', orderId);
                ajaxFormData.append('order_product_id', productId);
                ajaxFormData.append('quantity', quantity);

                fetch(BackendModule.getAdminUrl('sale/order/edit/updateProductQuantityInSessionAjax'), {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: ajaxFormData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.totals) {
                        BackendModule.updateTotalsDisplay(data.totals);
                    } else if (data.error) {
                        console.error('Грешка при актуализиране на количество:', data.error);
                        BackendModule.showOrderAlert('error', data.error);
                    }
                })
                .catch(error => {
                    console.error('Мрежова грешка при актуализиране на количество:', error);
                });
            },

            /**
             * Актуализиране на цена в OrderSession чрез AJAX
             */
            updateProductPriceInSession: function(productId, price) {
                // Получаване на order_id
                const orderIdInput = document.querySelector('input[name="order_id"]');
                const orderId = orderIdInput ? orderIdInput.value : null;

                if (!orderId) {
                    console.error('Не може да се намери ID на поръчката');
                    return;
                }

                const ajaxFormData = new FormData();
                ajaxFormData.append('order_id', orderId);
                ajaxFormData.append('order_product_id', productId);
                ajaxFormData.append('price', price);

                fetch(BackendModule.getAdminUrl('sale/order/edit/updateProductPriceInSessionAjax'), {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: ajaxFormData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.totals) {
                        BackendModule.updateTotalsDisplay(data.totals);
                    } else if (data.error) {
                        console.error('Грешка при актуализиране на цена:', data.error);
                        BackendModule.showOrderAlert('error', data.error);
                    }
                })
                .catch(error => {
                    console.error('Мрежова грешка при актуализиране на цена:', error);
                });
            },



            /**
             * Генерира композитен order_product_id на базата на product_id и опции
             *
             * @param {string|number} productId - ID на продукта
             * @param {Array} options - Масив с опции [{product_option_id, product_option_value_id}, ...]
             * @returns {string} - Композитен ID
             */
            generateCompositeProductId: function(productId, options = []) {
                let compositeId = productId.toString();

                if (options && options.length > 0) {
                    // Сортиране на опциите по product_option_id за консистентност
                    const sortedOptions = options.slice().sort((a, b) => {
                        const aOptionId = parseInt(a.product_option_id) || 0;
                        const bOptionId = parseInt(b.product_option_id) || 0;
                        return aOptionId - bOptionId;
                    });

                    sortedOptions.forEach(option => {
                        const optionId = option.product_option_id || '';
                        const valueId = option.product_option_value_id || '';
                        if (optionId && valueId) {
                            compositeId += '_' + optionId + '_' + valueId;
                        }
                    });
                }

                return compositeId;
            },

            /**
             * Актуализиране на общата сума за продукт
             */
            updateProductTotal: function(input) {
                const row = input.closest('tr');
                const quantity = parseFloat(row.querySelector('.product-quantity').value) || 0;
                const price = parseFloat(row.querySelector('.product-price').value) || 0;
                const total = quantity * price;

                row.querySelector('.product-total').textContent = total.toFixed(2);
            },

            /**
             * Инициализация на изчисляването на общи суми
             */
            initOrderTotalsCalculation: function() {
                // Автоматично изчисляване при промяна на данни
                const form = document.getElementById('order-edit-form');
                if (form) {
                    form.addEventListener('change', function(e) {
                        if (e.target.classList.contains('product-quantity') ||
                            e.target.classList.contains('product-price')) {
                            BackendModule.scheduleTotalsRecalculation();
                        }
                    });
                }
            },

            /**
             * Изчисляване на общите суми на поръчката чрез AJAX
             */
            calculateOrderTotals: function() {
                // Ако вече има активна заявка, пропускаме
                if (this.isCalculating) return;
                this.isCalculating = true;

                // Намиране на контейнера с общите суми
                const totalsContainer = document.getElementById('order-totals-container');
                if (!totalsContainer) {
                    this.isCalculating = false;
                    return;
                }

                // Събиране на данните за продуктите
                const products = [];
                const orderProductIds = []; // Масив за композитни order_product_id
                const productRows = document.querySelectorAll('#order-products-table tbody tr');

                productRows.forEach(row => {
                    const productId = row.querySelector('[name*="[product_id]"]')?.value;
                    const quantity = parseFloat(row.querySelector('.product-quantity')?.value) || 0;
                    const price = parseFloat(row.querySelector('.product-price')?.value) || 0;

                    // Събиране на композитните order_product_id от data-product-id атрибутите
                    const compositeProductId = row.querySelector('[data-product-id]')?.getAttribute('data-product-id');

                    if (productId && quantity > 0 && compositeProductId) {
                        products.push({
                            product_id: productId,
                            quantity: quantity,
                            price: price
                        });

                        // Добавяне на композитния ID в масива
                        orderProductIds.push(compositeProductId);
                    }
                });

                // Получаване на order_id
                const orderIdInput = document.querySelector('input[name="order_id"]');
                const orderId = orderIdInput ? orderIdInput.value : null;

                if (!orderId) {
                    console.error('Не може да се намери order_id');
                    this.isCalculating = false;
                    return;
                }

                // Показване на loading индикатор
                this.showTotalsLoading(true);

                // AJAX заявка към backend – изпращаме продуктите, order_id и композитните ID-та
                const formData = new FormData();
                formData.append('order_id', orderId);

                // Изпращаме продуктите като отделни полета products[index][field]
                products.forEach((p, i) => {
                    formData.append(`products[${i}][product_id]`, p.product_id);
                    formData.append(`products[${i}][quantity]`, p.quantity);
                    formData.append(`products[${i}][price]`, p.price);
                });

                // Изпращаме композитните order_product_id като масив
                orderProductIds.forEach((id, i) => {
                    formData.append(`order_product_ids[${i}]`, id);
                });

                fetch(BackendModule.getAdminUrl('sale/order/edit/recalculateOrderTotalsAjax'), {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {


                    console.log(data);

                    if (data.success && data.totals) {
                        const hasProducts = document.querySelectorAll('#order-products-table tbody tr').length > 0;
                        if (!hasProducts) {
                            this.resetTotalsUI();
                        } else {

                            console.log(data.totals);

                            this.updateTotalsDisplay(data.totals);
                        }
                        // Обновяване на състоянието на бутоните за купон/ваучер
                        BackendModule.refreshDiscountButtons(data.totals);
                    } else if (data.error) {
                        console.error('Грешка при преизчисляване на сумите:', data.error);
                        BackendModule.showOrderAlert('error', data.error);
                    }
                })
                .catch(error => {
                    console.error('Мрежова грешка при преизчисляване на сумите:', error);
                    BackendModule.showOrderAlert('error', 'Грешка при преизчисляване на сумите');
                })
                .finally(() => {
                    this.showTotalsLoading(false);
                    this.isCalculating = false;
                });
            },

            // Debounce scheduler за преизчисляване
            scheduleTotalsRecalculation: function() {
                // само за промени в quantity/price
                if (this.debounceTimer) {
                    clearTimeout(this.debounceTimer);
                }
                this.debounceTimer = setTimeout(() => {
                    if (!this.isCalculating) {
                        this.calculateOrderTotals();
                    }
                }, 350);
            },

            // Отмяна на текущия debounce, когато изчисляваме директно
            cancelTotalsDebounce: function() {
                if (this.debounceTimer) {
                    clearTimeout(this.debounceTimer);
                    this.debounceTimer = null;
                }
            },

            /**
             * Показва/скрива loading индикатор за сумите
             */
            showTotalsLoading: function(show) {
                const totalsContainer = document.getElementById('order-totals-container');
                if (!totalsContainer) return;

                if (show) {
                    totalsContainer.style.opacity = '0.6';
                    totalsContainer.style.pointerEvents = 'none';
                } else {
                    totalsContainer.style.opacity = '1';
                    totalsContainer.style.pointerEvents = 'auto';
                }
            },

            /**
             * Актуализира показването на общите суми
             */
            updateTotalsDisplay: function(totals) {

                const totalsContainer = document.getElementById('order-totals-container');
                if (!totalsContainer || !totals) return;

                // Изчистване на съществуващото съдържание
                totalsContainer.innerHTML = '';

                // Създаване на нови елементи за всяка сума
                // Поддръжка както за масив, така и за обект от тотали
                const iterateTotals = function(totalsInput) {
                    if (!totalsInput) {
                        return [];
                    }
                    if (Array.isArray(totalsInput)) {
                        return totalsInput;
                    }
                    if (typeof totalsInput === 'object') {
                        return Object.keys(totalsInput).map(function(key) {
                            const t = totalsInput[key] || {};
                            // ако липсва code в стойността, използваме ключа
                            if (typeof t === 'object' && t !== null) {
                                if (!('code' in t)) {
                                    t.code = key;
                                }
                                return t;
                            }
                            // ако е примитивна стойност, създаваме обект с минимални полета
                            return { code: key, title: key, value: t, text: String(t) };
                        });
                    }
                    return [];
                };



                const items = iterateTotals(totals);

                console.log(items);
                
                items.forEach(total => {
                    if(total === null) return;
                    const totalDiv = document.createElement('div');
                    totalDiv.className = total.code !== 'total' ? 'flex justify-between' : 'flex justify-between border-t border-gray-200 pt-2 mt-2';


                    // Специално форматиране за различните типове суми
                    let titleClass = 'font-semibold';
                    let valueClass = 'font-semibold';

                    if (total.code === 'total') {
                        titleClass += ' text-primary';
                        valueClass += ' text-primary';
                    } else if (total.code === 'voucher' || total.code === 'coupon') {
                        titleClass += ' text-sm text-green-600';
                        valueClass += ' text-sm text-green-600';
                    }
                    else {
                        titleClass += ' text-sm';
                        valueClass += ' text-sm';
                    }

                    totalDiv.innerHTML = `
                        <span class="${titleClass}">${total.title}:</span>
                        <span class="${valueClass}" data-total-value="${total.value}">${total.text}</span>
                    `;

                    totalsContainer.appendChild(totalDiv);
                });

                // Добавяне на визуална граница преди общата сума
                const totalElement = totalsContainer.querySelector('[data-total-code="total"]');
  
                
                if (totalElement) {
                    totalElement.classList.add('border-t', 'border-gray-200', 'pt-2', 'mt-2');
                }


                // Обновяване на състоянието на бутоните за купон/ваучер
                // Подаваме нормализирания масив, за да работи и при обектни тотали
                BackendModule.refreshDiscountButtons(items);
            },

            // Нулиране на визуалните тотали до 0.00
            resetTotalsUI: function() {
                const totalsContainer = document.getElementById('order-totals-container');
                if (!totalsContainer) return;
                totalsContainer.innerHTML = '';
                const zeros = [
                    { code: 'sub_total', title: 'Цена на продуктите', value: 0 },
                    { code: 'shipping', title: 'Доставка', value: 0 },
                    { code: 'voucher', title: 'Код за отстъпка (ваучер)', value: 0 },
                    { code: 'coupon', title: 'Код за отстъпка (купон)', value: 0 },
                    { code: 'total', title: 'Общо', value: 0 }
                ];
                zeros.forEach(t => {
                    const row = document.createElement('div');
                    row.className = 'flex justify-between';
                    row.setAttribute('data-total-code', t.code);
                    if(t.code === 'total') {
                        row.innerHTML = `
                        <span class=\"font-semibold\">${t.title}:</span>
                        <span class=\"text-primary font-semibold\" data-total-value=\"0\">0.00</span>
                    `;
                    }
                    else {
                    row.innerHTML = `
                        <span class=\"text-sm font-semibold\">${t.title}:</span>
                        <span class=\"text-sm font-semibold\" data-total-value=\"0\">0.00</span>
                    `;
                    }
                    totalsContainer.appendChild(row);
                });
            },

            /**
             * Настройване на infinite scroll за предложенията
             */
            setupInfiniteScroll: function() {
                const suggestionsDiv = document.getElementById('product-suggestions');

                // Премахване на предишни event listeners
                suggestionsDiv.removeEventListener('scroll', BackendModule.handleInfiniteScroll);

                // Добавяне на нов event listener
                suggestionsDiv.addEventListener('scroll', BackendModule.handleInfiniteScroll);
            },

            /**
             * Обработка на infinite scroll
             */
            handleInfiniteScroll: function() {
                const suggestionsDiv = document.getElementById('product-suggestions');
                const scrollTop = suggestionsDiv.scrollTop;
                const scrollHeight = suggestionsDiv.scrollHeight;
                const clientHeight = suggestionsDiv.clientHeight;

                // Проверка дали сме близо до дъното (в рамките на 50px)
                if (scrollTop + clientHeight >= scrollHeight - 50) {
                    if (BackendModule.productSearch &&
                        BackendModule.productSearch.hasMore &&
                        !BackendModule.productSearch.isLoading) {

                        const query = BackendModule.productSearch.query || '';
                        if (query) {
                            BackendModule.searchProducts(query, true);
                        } else {
                            BackendModule.loadMoreInitialProducts();
                        }
                    }
                }
            },

            /**
             * Зареждане на още първоначални продукти
             */
            loadMoreInitialProducts: function() {
                if (!BackendModule.productSearch ||
                    !BackendModule.productSearch.hasMore ||
                    BackendModule.productSearch.isLoading) {
                    return;
                }

                const start = BackendModule.productSearch.currentPage * 10;

                const formData = new FormData();
                formData.append('limit', '10');
                formData.append('start', start.toString());

                const searchUrl = window.location.origin + window.location.pathname + '?route=sale/order/searchProducts&user_token=' + BackendModule.config.userToken;

                BackendModule.productSearch.isLoading = true;
                BackendModule.showProductLoadingIndicator();

                fetch(searchUrl, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    BackendModule.productSearch.hasMore = data.has_more || false;
                    BackendModule.productSearch.currentPage++;
                    BackendModule.displayProductSuggestions(data.products || [], false);
                    BackendModule.hideProductLoadingIndicator();
                    BackendModule.productSearch.isLoading = false;
                })
                .catch(error => {
                    console.error('Error loading more products:', error);
                    BackendModule.hideProductLoadingIndicator();
                    BackendModule.productSearch.isLoading = false;
                });
            },

            /**
             * Показване на loading индикатор за продукти
             */
            showProductLoadingIndicator: function() {
                const suggestionsDiv = document.getElementById('product-suggestions');
                let loadingDiv = suggestionsDiv.querySelector('.loading-indicator');

                if (!loadingDiv) {
                    loadingDiv = document.createElement('div');
                    loadingDiv.className = 'loading-indicator p-3 text-center text-gray-500 text-sm';
                    loadingDiv.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Зареждане...';
                    suggestionsDiv.appendChild(loadingDiv);
                }
            },

            /**
             * Скриване на loading индикатор за продукти
             */
            hideProductLoadingIndicator: function() {
                const suggestionsDiv = document.getElementById('product-suggestions');
                const loadingDiv = suggestionsDiv.querySelector('.loading-indicator');

                if (loadingDiv) {
                    loadingDiv.remove();
                }
            },

            /**
             * Зареждане на опции за продукт
             */
            loadProductOptions: function(productId) {
                if (!productId) {
                    BackendModule.hideProductOptions();
                    return;
                }

                const formData = new FormData();
                formData.append('product_id', productId);

                const optionsUrl = window.location.origin + window.location.pathname + '?route=sale/order/getProductOptions&user_token=' + BackendModule.config.userToken;

                fetch(optionsUrl, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    BackendModule.displayProductOptions(data.options || []);
                })
                .catch(error => {
                    console.error('Error loading product options:', error);
                    BackendModule.hideProductOptions();
                });
            },

            /**
             * Показване на опции за продукт
             */
            displayProductOptions: function(options) {
                const optionsContainer = document.getElementById('product-options-container');
                const optionsList = document.getElementById('product-options-list');

                if (!optionsContainer || !optionsList) {
                    return;
                }

                if (!options || options.length === 0) {
                    optionsContainer.classList.add('hidden');
                    return;
                }

                optionsList.innerHTML = '';

                options.forEach(option => {
                    const optionDiv = document.createElement('div');
                    optionDiv.className = 'mb-3';

                    let optionHtml = `
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            ${option.name}${option.required ? ' *' : ''}
                        </label>
                    `;

                    if (option.type === 'select' && option.option_values && option.option_values.length > 0) {
                        optionHtml += `
                            <select name="product_options[${option.product_option_id}]" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" ${option.required ? 'required' : ''}>
                                <option value="">-- Изберете --</option>
                        `;

                        option.option_values.forEach(value => {
                            const priceText = value.price && parseFloat(value.price) !== 0 ?
                                ` (${value.price_prefix}${parseFloat(value.price).toFixed(2)} лв.)` : '';
                            optionHtml += `<option value="${value.product_option_value_id}" data-price="${value.price || 0}" data-prefix="${value.price_prefix || ''}">${value.name}${priceText}</option>`;
                        });

                        optionHtml += '</select>';
                    } else if (option.type === 'radio' && option.option_values && option.option_values.length > 0) {
                        optionHtml += '<div class="space-y-2">';

                        option.option_values.forEach((value, index) => {
                            const priceText = value.price && parseFloat(value.price) !== 0 ?
                                ` (${value.price_prefix}${parseFloat(value.price).toFixed(2)} лв.)` : '';
                            optionHtml += `
                                <label class="flex items-center">
                                    <input type="radio" name="product_options[${option.product_option_id}]" value="${value.product_option_value_id}" data-price="${value.price || 0}" data-prefix="${value.price_prefix || ''}" class="mr-2" ${option.required && index === 0 ? 'required' : ''}>
                                    <span class="text-sm">${value.name}${priceText}</span>
                                </label>
                            `;
                        });

                        optionHtml += '</div>';
                    } else if (option.type === 'checkbox' && option.option_values && option.option_values.length > 0) {
                        optionHtml += '<div class="space-y-2">';

                        option.option_values.forEach(value => {
                            const priceText = value.price && parseFloat(value.price) !== 0 ?
                                ` (${value.price_prefix}${parseFloat(value.price).toFixed(2)} лв.)` : '';
                            optionHtml += `
                                <label class="flex items-center">
                                    <input type="checkbox" name="product_options[${option.product_option_id}][]" value="${value.product_option_value_id}" data-price="${value.price || 0}" data-prefix="${value.price_prefix || ''}" class="mr-2">
                                    <span class="text-sm">${value.name}${priceText}</span>
                                </label>
                            `;
                        });

                        optionHtml += '</div>';
                    } else if (option.type === 'text' || option.type === 'textarea') {
                        const inputType = option.type === 'textarea' ? 'textarea' : 'input';
                        const inputAttrs = option.type === 'textarea' ? 'rows="3"' : 'type="text"';

                        optionHtml += `
                            <${inputType} name="product_options[${option.product_option_id}]" ${inputAttrs} class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" ${option.required ? 'required' : ''}></${inputType}>
                        `;
                    }

                    optionDiv.innerHTML = optionHtml;
                    optionsList.appendChild(optionDiv);
                });

                // Запазване на оригиналната цена на продукта
                const priceInput = document.querySelector('[name="price"]');
                if (priceInput && !priceInput.hasAttribute('data-original-price')) {
                    priceInput.setAttribute('data-original-price', priceInput.value);
                }

                optionsContainer.classList.remove('hidden');

                // Добавяне на event listeners за автоматично обновяване на цената
                BackendModule.addOptionEventListeners();

                // Първоначално обновяване на цената (ако има предварително избрани опции)
                setTimeout(() => {
                    BackendModule.updateProductPrice();
                }, 100);
            },

            /**
             * Скриване на опции за продукт
             */
            hideProductOptions: function() {
                const optionsContainer = document.getElementById('product-options-container');
                if (optionsContainer) {
                    optionsContainer.classList.add('hidden');
                }

                // Възстановяване на оригиналната цена
                const priceInput = document.querySelector('[name="price"]');
                if (priceInput && priceInput.hasAttribute('data-original-price')) {
                    priceInput.value = priceInput.getAttribute('data-original-price');
                }
            },

            /**
             * Обновяване на цената на продукта според избраните опции
             */
            updateProductPrice: function() {
                const priceInput = document.querySelector('[name="price"]');
                if (!priceInput) return;

                // Получаване на оригиналната цена на продукта
                const originalPrice = parseFloat(priceInput.getAttribute('data-original-price') || priceInput.value);
                let totalPrice = originalPrice;

                // Събиране на цените от всички избрани опции
                const optionInputs = document.querySelectorAll('#product-options-list input, #product-options-list select');

                optionInputs.forEach(input => {
                    let optionPrice = 0;
                    let pricePrefix = '';

                    if (input.type === 'radio') {
                        if (input.checked) {
                            optionPrice = parseFloat(input.getAttribute('data-price') || 0);
                            pricePrefix = input.getAttribute('data-prefix') || '';
                        }
                    } else if (input.type === 'checkbox') {
                        if (input.checked) {
                            optionPrice = parseFloat(input.getAttribute('data-price') || 0);
                            pricePrefix = input.getAttribute('data-prefix') || '';
                        }
                    } else if (input.tagName === 'SELECT' && input.value) {
                        const selectedOption = input.options[input.selectedIndex];
                        if (selectedOption && selectedOption.value) {
                            optionPrice = parseFloat(selectedOption.getAttribute('data-price') || 0);
                            pricePrefix = selectedOption.getAttribute('data-prefix') || '';
                        }
                    }

                    // Прилагане на цената според префикса
                    if (optionPrice !== 0) {
                        if (pricePrefix === '+' || pricePrefix === '') {
                            // Ако няма префикс или е "+", се добавя
                            totalPrice += optionPrice;
                        } else if (pricePrefix === '-') {
                            totalPrice -= optionPrice;
                        }
                    }
                });

                // Обновяване на полето за цена (не може да бъде по-малко от 0)
                priceInput.value = Math.max(0, totalPrice).toFixed(2);
            },

            /**
             * Добавяне на event listeners за опциите
             */
            addOptionEventListeners: function() {
                // Премахване на стари event listeners за да избегнем дублиране
                const optionInputs = document.querySelectorAll('#product-options-list input, #product-options-list select');

                optionInputs.forEach(input => {
                    // Премахване на стар listener ако съществува
                    input.removeEventListener('change', BackendModule.updateProductPrice);

                    // Добавяне на нов listener
                    if (input.type === 'radio' || input.type === 'checkbox' || input.tagName === 'SELECT') {
                        input.addEventListener('change', BackendModule.updateProductPrice);
                    }
                });
            },

            /**
             * Получаване на избраните опции за продукт
             */
            getSelectedProductOptions: function() {
                const options = [];
                const optionInputs = document.querySelectorAll('#product-options-list input, #product-options-list select, #product-options-list textarea');

                optionInputs.forEach(input => {
                    if (input.type === 'radio' || input.type === 'checkbox') {
                        if (input.checked) {
                            const optionName = input.closest('.mb-3').querySelector('label').textContent.replace(' *', '');
                            const optionValue = input.nextElementSibling.textContent.trim();

                            options.push({
                                product_option_id: input.name.match(/\[(\d+)\]/)[1],
                                product_option_value_id: input.value,
                                name: optionName,
                                value: optionValue,
                                type: input.type
                            });
                        }
                    } else if (input.tagName === 'SELECT') {
                        if (input.value) {
                            const optionName = input.closest('.mb-3').querySelector('label').textContent.replace(' *', '');
                            const selectedOption = input.options[input.selectedIndex];
                            const optionValue = selectedOption.textContent.replace(/\s*\([^)]*\)\s*$/, ''); // Премахване на цената от текста

                            options.push({
                                product_option_id: input.name.match(/\[(\d+)\]/)[1],
                                product_option_value_id: input.value,
                                name: optionName,
                                value: optionValue,
                                type: 'select'
                            });
                        }
                    } else if (input.value.trim()) {
                        const optionName = input.closest('.mb-3').querySelector('label').textContent.replace(' *', '');

                        options.push({
                            product_option_id: input.name.match(/\[(\d+)\]/)[1],
                            product_option_value_id: 0,
                            name: optionName,
                            value: input.value.trim(),
                            type: input.tagName.toLowerCase() === 'textarea' ? 'textarea' : 'text'
                        });
                    }
                });

                return options;
            },

            /**
             * Генериране на hidden input полета за опции
             */
            generateOptionsHiddenFields: function(timestamp, options) {
                let hiddenFields = '';

                options.forEach((option, index) => {
                    hiddenFields += `
                        <input type="hidden" name="products[${timestamp}][options][${index}][product_option_id]" value="${option.product_option_id}">
                        <input type="hidden" name="products[${timestamp}][options][${index}][product_option_value_id]" value="${option.product_option_value_id}">
                        <input type="hidden" name="products[${timestamp}][options][${index}][name]" value="${option.name}">
                        <input type="hidden" name="products[${timestamp}][options][${index}][value]" value="${option.value}">
                        <input type="hidden" name="products[${timestamp}][options][${index}][type]" value="${option.type}">
                    `;
                });

                return hiddenFields;
            },

            /**
             * Инициализация на функционалността за ваучери и купони
             */
            initDiscountFunctionality: function() {
                // Бутон за прилагане на ваучер
                const applyVoucherBtn = document.getElementById('apply-voucher-btn');
                if (applyVoucherBtn) {
                    applyVoucherBtn.addEventListener('click', function() {
                        const voucherCode = document.getElementById('voucher-code-input').value.trim();
                        if (voucherCode) {
                            BackendModule.applyVoucher(voucherCode);
                        } else {
                            BackendModule.showDiscountMessage('voucher', 'error', 'Моля въведете ваучер код');
                        }
                    });
                }

                // Бутон за прилагане на купон
                const applyCouponBtn = document.getElementById('apply-coupon-btn');
                if (applyCouponBtn) {
                    applyCouponBtn.addEventListener('click', function() {
                        const couponCode = document.getElementById('coupon-code-input').value.trim();
                        if (couponCode) {
                            BackendModule.applyCoupon(couponCode);
                        } else {
                            BackendModule.showDiscountMessage('coupon', 'error', 'Моля въведете купон код');
                        }
                    });
                }
            },

            /**
             * Прилагане на ваучер код
             */
            applyVoucher: function(voucherCode) {
                const orderId = this.getOrderId();
                if (!orderId) {
                    BackendModule.showDiscountMessage('voucher', 'error', 'Невалиден ID на поръчка');
                    return;
                }

                const applyBtn = document.getElementById('apply-voucher-btn');
                const originalText = applyBtn.innerHTML;

                // Показване на loading състояние
                applyBtn.disabled = true;
                applyBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Прилагане...';

                const formData = new FormData();
                formData.append('order_id', orderId);
                formData.append('voucher_code', voucherCode);

                const applyUrl = window.location.origin + window.location.pathname + '?route=sale/order/applyVoucher&user_token=' + BackendModule.config.userToken;

                fetch(applyUrl, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    applyBtn.disabled = false;
                    applyBtn.innerHTML = originalText;

                    if (data.success) {
                        BackendModule.showDiscountMessage('voucher', 'success', data.success);
                        if (data.totals) {
                            BackendModule.updateTotalsDisplay(data.totals);
                            BackendModule.refreshDiscountButtons(data.totals);
                        }
                    } else if (data.error) {
                        BackendModule.showDiscountMessage('voucher', 'error', data.error);
                    }
                })
                .catch(error => {
                    applyBtn.disabled = false;
                    applyBtn.innerHTML = originalText;
                    BackendModule.showDiscountMessage('voucher', 'error', 'Възникна грешка при прилагане на ваучера');
                });
            },

            /**
             * Прилагане на купон код
             */
            applyCoupon: function(couponCode) {
                const orderId = this.getOrderId();
                if (!orderId) {
                    BackendModule.showDiscountMessage('coupon', 'error', 'Невалиден ID на поръчка');
                    return;
                }

                const applyBtn = document.getElementById('apply-coupon-btn');
                const originalText = applyBtn.innerHTML;

                // Показване на loading състояние
                applyBtn.disabled = true;
                applyBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Прилагане...';

                const formData = new FormData();
                formData.append('order_id', orderId);
                formData.append('coupon_code', couponCode);

                // Събиране на актуалните продуктни данни от формата (същата логика като в recalculateOrderTotals)
                const products = [];
                const orderProductIds = [];
                const productRows = document.querySelectorAll('#order-products-table tbody tr');

                productRows.forEach(row => {
                    const productId = row.querySelector('[name*="[product_id]"]')?.value;
                    const quantity = parseFloat(row.querySelector('.product-quantity')?.value) || 0;
                    const price = parseFloat(row.querySelector('.product-price')?.value) || 0;

                    // Събиране на композитните order_product_id от data-product-id атрибутите
                    const compositeProductId = row.querySelector('[data-product-id]')?.getAttribute('data-product-id');

                    if (productId && quantity > 0 && compositeProductId) {
                        products.push({
                            product_id: productId,
                            quantity: quantity,
                            price: price
                        });

                        // Добавяне на композитния ID в масива
                        orderProductIds.push(compositeProductId);
                    }
                });

                // Изпращаме продуктите като отделни полета products[index][field]
                products.forEach((p, i) => {
                    formData.append(`products[${i}][product_id]`, p.product_id);
                    formData.append(`products[${i}][quantity]`, p.quantity);
                    formData.append(`products[${i}][price]`, p.price);
                });

                // Изпращаме композитните order_product_id като масив
                orderProductIds.forEach((id, i) => {
                    formData.append(`order_product_ids[${i}]`, id);
                });

                const applyUrl = window.location.origin + window.location.pathname + '?route=sale/order/applyCoupon&user_token=' + BackendModule.config.userToken;

                fetch(applyUrl, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    applyBtn.disabled = false;
                    applyBtn.innerHTML = originalText;

                    if (data.success) {
                        BackendModule.showDiscountMessage('coupon', 'success', data.success);
                        if (data.totals) {
                            BackendModule.updateTotalsDisplay(data.totals);
                            BackendModule.refreshDiscountButtons(data.totals);
                        }
                    } else if (data.error) {
                        BackendModule.showDiscountMessage('coupon', 'error', data.error);
                    }
                })
                .catch(error => {
                    applyBtn.disabled = false;
                    applyBtn.innerHTML = originalText;
                    BackendModule.showDiscountMessage('coupon', 'error', 'Възникна грешка при прилагане на купона');
                });
            },

            /**
             * Показва/скрива съобщение за купон/ваучер
             */
            // Показва/скрива съобщение за купон/ваучер (обединена имплементация)
            showDiscountMessage: function(type, status, message) {
                const messageContainer = document.getElementById(type + '-message');
                if (!messageContainer) return;
                messageContainer.className = 'mt-2 text-sm ' + (status === 'success' ? 'text-green-600' : 'text-red-600');
                messageContainer.textContent = message;
                messageContainer.classList.remove('hidden');
                setTimeout(() => { messageContainer.classList.add('hidden'); }, 5000);
            },

            /**
             * Обновява UI за ваучер/купон според наличието на активни кодове
             */
            refreshDiscountButtons: function(totals) {
                // Нормализиране: приемаме както масив, така и обект
                const normalizeTotals = function(input) {
                    if (!input) return [];
                    if (Array.isArray(input)) return input;
                    if (typeof input === 'object') {
                        return Object.keys(input).map(function(key) {
                            const t = input[key] || {};
                            if (typeof t === 'object' && t !== null) {
                                if (!('code' in t)) { t.code = key; }
                                return t;
                            }
                            return { code: key, title: key, value: t, text: String(t) };
                        });
                    }
                    return [];
                };

                const items = normalizeTotals(totals);


                const hasVoucher = items.some(function(t) { return t.code === 'voucher' && t.value !== 0; });
                const hasCoupon  = items.some(function(t) { 
                    return t.code === 'coupon' && t.value !== 0; 
                });


                // Ваучер UI управление
                const voucherInputWrap = document.getElementById('voucher-input-wrap');
                const voucherCardWrap = document.getElementById('voucher-card-wrap');

                if (voucherInputWrap && voucherCardWrap) {
                    if (hasVoucher) {
                        // Скрий input, покажи карта
                        voucherInputWrap.classList.add('hidden');
                        voucherCardWrap.classList.remove('hidden');

                        // Обнови съдържанието на картата
                        const voucherTotal = items.find(function(t) { return t.code === 'voucher'; });
                        if (voucherTotal) {
                            const voucherCard = voucherCardWrap.querySelector('.bg-green-50');
                            if (voucherCard) {
                                const voucherInfo = voucherCard.querySelector('.text-green-800');
                                const voucherValue = voucherCard.querySelector('.text-green-700');
                                const hiddenInput = voucherCard.querySelector('input[type="hidden"]');

                                if (voucherInfo) voucherInfo.textContent = `Приложен ваучер: ${document.getElementById('voucher-code-input').value}`;
                                if (voucherValue) voucherValue.textContent = `Стойност: ${voucherTotal.text}`;
                                if (hiddenInput) hiddenInput.value = document.getElementById('voucher-code-input').value;
                            }
                        }
                    } else {
                        // Покажи input, скрий карта
                        voucherInputWrap.classList.remove('hidden');
                        voucherCardWrap.classList.add('hidden');
                    }
                }

                // Купон UI управление
                const couponInputWrap = document.getElementById('coupon-input-wrap');
                const couponCardWrap = document.getElementById('coupon-card-wrap');

                if (couponInputWrap && couponCardWrap) {
                    if (hasCoupon) {
                        // Скрий input, покажи карта
                        couponInputWrap.classList.add('hidden');
                        couponCardWrap.classList.remove('hidden');

                        // Обнови съдържанието на картата
                        const couponTotal = totals ? Object.values(totals).find(t => t.code === 'coupon') : null;
                        if (couponTotal) {
                            const couponCard = couponCardWrap.querySelector('.bg-blue-50');
                            if (couponCard) {
                                const couponInfo = couponCard.querySelector('.text-blue-800');
                                const couponValue = couponCard.querySelector('.text-blue-700');
                                const hiddenInput = couponCard.querySelector('input[type="hidden"]');

                                if (couponInfo) couponInfo.textContent = `Приложен купон: ${document.getElementById('coupon-code-input').value}`;
                                if (couponValue) couponValue.textContent = `Стойност: ${couponTotal.text}`;
                                if (hiddenInput) hiddenInput.value = document.getElementById('coupon-code-input').value;
                            }
                        }
                    } else {
                        // Покажи input, скрий карта
                        couponInputWrap.classList.remove('hidden');
                        couponCardWrap.classList.add('hidden');
                    }
                }
            },

            /**
             * Премахване на ваучер
             */
            removeVoucher: function() {
                const orderId = this.getOrderId();
                if (!orderId) return;

                const url = window.location.origin + window.location.pathname + '?route=sale/order/edit/removeVoucherAjax&user_token=' + BackendModule.config.userToken;
                const formData = new FormData();
                formData.append('order_id', orderId);

                fetch(url, { 
                    method: 'POST', 
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData 
                })
                  .then(r => r.json())
                  .then(data => {
                    if (data.success) {
                      // Изчисти input полето
                      const voucherInput = document.getElementById('voucher-code-input');
                      if (voucherInput) voucherInput.value = '';

                      // Обнови totals и UI
                      BackendModule.updateTotalsDisplay(data.totals);
                      BackendModule.refreshDiscountButtons(data.totals);

                      // Покажи съобщение за успех
                      BackendModule.showDiscountMessage('voucher', 'success', 'Ваучерът е премахнат успешно');
                    } else if (data.error) {
                      BackendModule.showDiscountMessage('voucher', 'error', data.error);
                    }
                  })
                  .catch(err => {
                    console.error('removeVoucherAjax error', err);
                    BackendModule.showDiscountMessage('voucher', 'error', 'Възникна грешка при премахване на ваучера');
                  });
            },

            /**
             * Премахване на купон
             */
            removeCoupon: function() {
                const orderId = this.getOrderId();
                if (!orderId) return;

                const url = window.location.origin + window.location.pathname + '?route=sale/order/edit/removeCouponAjax&user_token=' + BackendModule.config.userToken;
                const formData = new FormData();
                formData.append('order_id', orderId);

                fetch(url, { 
                    method: 'POST', 
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData 
                })
                  .then(r => r.json())
                  .then(data => {
                    if (data.success) {
                      // Изчисти input полето
                      const couponInput = document.getElementById('coupon-code-input');
                      if (couponInput) couponInput.value = '';

                      // Обнови totals и UI
                      BackendModule.updateTotalsDisplay(data.totals);
                      BackendModule.refreshDiscountButtons(data.totals);

                      // Покажи съобщение за успех
                      BackendModule.showDiscountMessage('coupon', 'success', 'Купонът е премахнат успешно');
                    } else if (data.error) {
                      BackendModule.showDiscountMessage('coupon', 'error', data.error);
                    }
                  })
                  .catch(err => {
                    console.error('removeCouponAjax error', err);
                    BackendModule.showDiscountMessage('coupon', 'error', 'Възникна грешка при премахване на купона');
                  });
            },


            /**
             * Получаване на ID на поръчката
             */
            getOrderId: function() {
                const orderIdInput = document.querySelector('input[name="order_id"]');
                if (orderIdInput) {
                    return orderIdInput.value;
                }

                // Алтернативно извличане от URL
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('order_id') || '';
            },



            /**
             * Обновяване на показването на общите суми
             */
            updateOrderTotalsDisplay: function(totals) {
                const totalsContainer = document.getElementById('order-totals-container');
                if (!totalsContainer || !totals) return;

                // Изчистване на съществуващото съдържание
                totalsContainer.innerHTML = '';

                // Нормализиране на тоталите - поддръжка както за масив, така и за обект
                const iterateTotals = function(totalsInput) {
                    if (!totalsInput) {
                        return [];
                    }
                    if (Array.isArray(totalsInput)) {
                        return totalsInput;
                    }
                    if (typeof totalsInput === 'object') {
                        return Object.keys(totalsInput).map(function(key) {
                            const t = totalsInput[key] || {};
                            // ако липсва code в стойността, използваме ключа
                            if (typeof t === 'object' && t !== null) {
                                if (!('code' in t)) {
                                    t.code = key;
                                }
                                return t;
                            }
                            // ако е примитивна стойност, създаваме обект с минимални полета
                            return { code: key, title: key, value: t, text: String(t) };
                        });
                    }
                    return [];
                };

                const normalizedTotals = iterateTotals(totals);

                console.log(normalizedTotals);

                // Добавяне на новите суми в правилния ред
                normalizedTotals.forEach(total => {
                    if(total === null) return;
                    const totalDiv = document.createElement('div');
                    totalDiv.className = total.code !== 'total' ? 'flex justify-between' : 'flex justify-between border-t border-gray-200 pt-2 mt-2';

                    console.log(total.code);
                    console.log(totalDiv.className);

                    // Специално форматиране за различните типове суми
                    let titleClass = 'text-sm';
                    let valueClass = 'text-sm';

                    if (total.code === 'total') {
                        titleClass = ' font-semibold';
                        valueClass += ' font-semibold';
                    } else if (total.code === 'voucher' || total.code === 'coupon') {
                        titleClass += ' text-green-600';
                        valueClass += ' text-green-600';
                    }

                    totalDiv.innerHTML = `
                        <span class="${titleClass}">${total.title}:</span>
                        <span class="${valueClass}" data-total-value="${total.value}">${total.text}</span>
                    `;

                    totalsContainer.appendChild(totalDiv);
                });

                // Добавяне на визуална граница преди общата сума
                const totalElement = totalsContainer.querySelector('[data-total-code="total"]');
  
                
                if (totalElement) {
                    totalElement.classList.add('border-t', 'border-gray-200', 'pt-2', 'mt-2');
                }

                console.log(totalElement);
                console.log(totalElement.classList);
            },

            /**
             * Показване на съобщение специфично за поръчки
             */
            showOrderAlert: function(type, message, duration = 3) {
                // Използваме същата функция като в основния модул, но с префикс за поръчки
                if (typeof this.showAlert === 'function') {
                    this.showAlert(type, message, duration);
                } else {
                    // Fallback ако основната функция не е налична
                    alert(message);
                }
            }
        });

        // Инициализиране на productSearch обекта
        BackendModule.productSearch = {
            currentPage: 0,
            hasMore: true,
            isLoading: false,
            query: ''
        };
    }

})();
