<?php

namespace Theme25\Backend\Controller\Setting\Setting;

/**
 * Sub-контролер за настройки за доставка
 *
 * Този контролер управлява логиката за показване и обработка на настройките за доставка,
 * включително shipping методи, зони за доставка, цени и други настройки свързани с доставката.
 *
 * @package Theme25\Backend\Controller\Setting\Setting
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
class Delivery extends \Theme25\ControllerSubMethods {

    /**
     * Конструктор - зарежда необходимите JavaScript файлове
     */
    public function __construct($registry) {
        parent::__construct($registry);
        $this->loadScripts();
    }

    /**
     * Зарежда необходимите JavaScript файлове
     */
    protected function loadScripts() {
        // Зареждаме основните JavaScript файлове за delivery функционалност
        $scripts = [
            'settings-main.js',
            'settings-delivery.js'
        ];

        $this->addBackendScriptWithVersion($scripts, 'footer');
    }

    /**
     * Основен метод който се извиква при достъп до route-а
     * Обработва delivery_method_id параметъра и зарежда съответната страница
     */
    public function execute() {
        // Проверяваме дали има delivery_method_id параметър
        $delivery_method_id = $this->requestGet('delivery_method_id');

        if ($delivery_method_id) {
            // Ако има delivery_method_id, зареждаме специфичната страница за куриера
            $this->loadCourierConfigPage($delivery_method_id);
        } else {
            // Ако няма delivery_method_id, пренасочваме към основните настройки
            $this->response->redirect($this->getAdminLink('setting/setting', 'tab=delivery'));
        }
    }

    /**
     * Зарежда страницата за конфигуриране на специфичен куриер
     *
     * @param int $delivery_method_id ID на куриера
     */
    public function loadCourierConfigPage($delivery_method_id) {
        try {
            $this->loadModelsAs([
                'tool/image' => 'imageModel',
                'setting/delivery' => 'deliveryModel'
            ]);

            // Получаваме данните за конкретния куриер по ID
            $method_data = $this->deliveryModel->getDeliveryMethodById($delivery_method_id);

            if (!$method_data) {
                // Ако куриерът не е намерен, пренасочваме към общия преглед
                $this->response->redirect($this->getAdminLink('setting/setting', 'tab=delivery'));
                return;
            }

            // Получаваме кода на куриера от ID
            $method_code = $method_data['code'];

            // Задаваме заглавието на страницата
            $this->setTitle('Настройки за ' . $method_data['name']);

            // Инициализиране на административните данни
            $this->initAdminData();

            // Получаваме конфигурационните полета за куриера
            $config_fields = $this->deliveryModel->getCourierConfigFields($method_code);

            // Получаваме стандартните настройки
            $default_settings = $this->deliveryModel->getCourierDefaultSettings($method_code);

            // Получаваме текущите настройки от базата данни
            $database_settings = $this->deliveryModel->getCourierSettings($method_code);

            // Merge-ваме стандартните настройки с реалните настройки от базата данни
            $current_settings = array_merge($default_settings, $database_settings);

            // Обработваме полетата (options_source/custom) с достъп до текущите настройки
            $config_fields = $this->processOptionsSourceFields($config_fields, $method_code, $current_settings);

            // Организиране на полетата в табове
            $tabs = $this->organizeFieldsIntoTabs($config_fields);

            // Задаваме данните за шаблона
            $this->setData([
                'delivery_method_id' => $delivery_method_id,
                'method_code' => $method_code,
                'method_data' => $method_data,
                'config_fields' => $config_fields,
                'current_settings' => $current_settings,
                'tabs' => $tabs,
                'can_test_connection' => $this->deliveryModel->canTestCourierConnection($method_code, $current_settings),
                'pricing_types' => $this->deliveryModel->getPricingTypes(),
                'save_url' => $this->getAdminLink('setting/setting/delivery_save'),
                'test_connection_url' => $this->getAdminLink('setting/setting/test_delivery_connection'),
                'back_url' => $this->getAdminLink('setting/setting', 'tab=delivery')
            ]);

            // Рендиране на специфичния шаблон за куриера (използваме кода за шаблона)
            $this->renderTemplateWithDataAndOutput('setting/delivery/' . $method_code);

        } catch (Exception $e) {
            error_log('Грешка при зареждане на страницата за куриер: ' . $e->getMessage());
            $this->response->redirect($this->getAdminLink('setting/setting', 'tab=delivery'));
        }
    }

    /**
     * Подготвя данните за настройките за доставка (за обратна съвместимост)
     */
    public function prepareData() {
        // Проверяваме дали има delivery_method_id параметър за специфична подстраница
        // $delivery_method_id = $this->requestGet('delivery_method_id');

        // if ($delivery_method_id) {
        //     // Ако има delivery_method_id (цифрово ID), подготвяме данни за специфичния куриер
        //     $this->prepareSpecificCourierDataById($delivery_method_id);
        // } else {
            // Ако няма delivery_method_id, показваме общия преглед
            $this->prepareDeliveryMethodsData()
                //  ->preparePricingTypesData()
                 ->prepareUrlsAndActions();
        // }
    }

    /**
     * Подготвя данните за специфичен куриер по ID
     *
     * @param int $delivery_method_id ID на куриера
     * @return $this За верижно извикване на методи
     */
    private function prepareSpecificCourierDataById($delivery_method_id) {
        try {
            $this->loadModelsAs([
                'tool/image' => 'imageModel',
                'setting/delivery' => 'deliveryModel'
            ]);

            // Получаваме данните за конкретния куриер по ID
            $method_data = $this->deliveryModel->getDeliveryMethodById($delivery_method_id);

            if (!$method_data) {
                // Ако куриерът не е намерен, пренасочваме към общия преглед
                $this->response->redirect($this->getAdminLink('setting/setting', 'tab=delivery'));
                return $this;
            }

            // Получаваме кода на куриера от ID
            $method_code = $method_data['code'];

            // Получаваме конфигурационните полета за куриера
            $config_fields = $this->deliveryModel->getCourierConfigFields($method_code);

            // Получаваме стандартните настройки
            $default_settings = $this->deliveryModel->getCourierDefaultSettings($method_code);

            // Получаваме текущите настройки от базата данни
            $database_settings = $this->deliveryModel->getCourierSettings($method_code);

            // Merge-ваме стандартните настройки с реалните настройки от базата данни
            $current_settings = array_merge($default_settings, $database_settings);

            // Винаги добавяме основните настройки от основната таблица
            $current_settings['pricing_type'] = $method_data['pricing_type'] ?? 'calculated';
            $current_settings['fixed_price'] = $method_data['fixed_price'] ?? '0';

            // Подготовка на табовете за организация на полетата
            $tabs = $this->organizeFieldsIntoTabs($config_fields);

            // Задаваме данните за шаблона
            $this->setData([
                'delivery_method_id' => $delivery_method_id,
                'method_code' => $method_code,
                'method_data' => $method_data,
                'config_fields' => $config_fields,
                'current_settings' => $current_settings,
                'tabs' => $tabs,
                'can_test_connection' => $this->deliveryModel->canTestCourierConnection($method_code, $current_settings),
                'pricing_types' => $this->deliveryModel->getPricingTypes(),
                'save_url' => $this->getAdminLink('setting/setting/delivery_save'),
                'test_connection_url' => $this->getAdminLink('setting/setting/test_delivery_connection'),
                'back_url' => $this->getAdminLink('setting/setting', 'tab=delivery')
            ]);

        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на данни за куриера: ' . $e->getMessage());

            // При грешка пренасочваме към общия преглед
            $this->response->redirect($this->getAdminLink('setting/setting', 'tab=delivery'));
        }

        return $this;
    }

    /**
     * Подготвя данните за методи на доставка
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareDeliveryMethodsData() {
        try {
            $this->loadModelsAs([
                'tool/image' => 'imageModel',
                'setting/delivery' => 'deliveryModel'
            ]);

            $delivery_methods = $this->deliveryModel->getDeliveryMethods();

            // Добавяне на допълнителна информация за всеки метод
            foreach ($delivery_methods as $code => &$method) {
                $method['icon'] = $this->getMethodIcon($code);
                $method['logo_url'] = $this->getMethodLogo($method['logo_path']);
                $method['config_fields'] = $this->deliveryModel->getCourierConfigFields($code);
            }

            $this->setData('delivery_methods', $delivery_methods);

        } catch (Exception $e) {
            $this->setError('Грешка при зареждане на методи за доставка: ' . $e->getMessage());

            // Задаване на стойности по подразбиране
            $this->setData('delivery_methods', []);
        }

        return $this;
    }

    /**
     * Получава иконата за метод на доставка
     */
    private function getMethodIcon($code) {
        $icons = [
            'econt' => 'ri-truck-line',
            'speedy' => 'ri-rocket-line',
            'boxnow' => 'ri-archive-line',
            'sameday' => 'ri-time-line',
            'pickup' => 'ri-store-line'
        ];

        return isset($icons[$code]) ? $icons[$code] : 'ri-truck-line';
    }

    /**
     * Получава логото за метод на доставка
     */
    private function getMethodLogo($logo_path) {
        if (empty($logo_path)) {
            return '';
        }
        $resized_image_url = $this->imageModel->resize($logo_path, 150, 150, true);
        return $resized_image_url;
    }

    /**
     * Подготвя данните за типове цени
     *
     * @return $this За верижно извикване на методи
     */
    private function preparePricingTypesData() {
        try {
            $this->loadModelAs('setting/delivery', 'deliveryModel');
            $pricing_types = $this->deliveryModel->getPricingTypes();

            $this->setData('pricing_types', $pricing_types);

        } catch (Exception $e) {
            // Fallback данни
            $this->setData('pricing_types', [
                'fixed' => 'Фиксирана цена',
                'free' => 'Безплатна доставка',
                'calculated' => 'Изчислена от куриерския сървър'
            ]);
        }

        return $this;
    }

    /**
     * Подготвя URL-и за AJAX заявки и действия
     *
     * @return $this За верижно извикване на методи
     */
    private function prepareUrlsAndActions() {
        $urls = [
            'save_url' => $this->getAdminLink('setting/setting/delivery_save'),
            'test_connection_url' => $this->getAdminLink('setting/setting/test_delivery_connection'),
            'test_shipping_url' => $this->getAdminLink('setting/setting/test_shipping_method'),
            'toggle_method_url' => $this->getAdminLink('setting/setting/toggle_delivery_method'),
            'configure_method_url' => $this->getAdminLink('setting/setting/configure_delivery_method'),
            'save_method_config_url' => $this->getAdminLink('setting/setting/save_delivery_method_config'),

            // URL-и за суб-контролерите
            'econt_config_url' => $this->getAdminLink('setting/setting/delivery/econt'),
            'econt_save_url' => $this->getAdminLink('setting/setting/delivery/econt/save'),
            'speedy_config_url' => $this->getAdminLink('setting/setting/delivery/speedy'),
            'speedy_save_url' => $this->getAdminLink('setting/setting/delivery/speedy/save'),
            'boxnow_config_url' => $this->getAdminLink('setting/setting/delivery/boxnow'),
            'boxnow_save_url' => $this->getAdminLink('setting/setting/delivery/boxnow/save'),
            'sameday_config_url' => $this->getAdminLink('setting/setting/delivery/sameday'),
            'sameday_save_url' => $this->getAdminLink('setting/setting/delivery/sameday/save'),
            'pickup_config_url' => $this->getAdminLink('setting/setting/delivery/pickup'),
            'pickup_save_url' => $this->getAdminLink('setting/setting/delivery/pickup/save')
        ];

        foreach ($urls as $key => $url) {
            $this->setData($key, $url);
        }

        return $this;
    }

    /**
     * Запазва настройките за доставка
     *
     * @return array
     */
    public function saveDeliverySettings() {
        try {
            $this->loadModelAs('setting/delivery', 'deliveryModel');

            $post_data = $this->requestPost();
            $result = $this->deliveryModel->saveAllDeliverySettings($post_data);

            return $result;

        } catch (Exception $e) {
            return ['error' => 'Грешка при запазване: ' . $e->getMessage()];
        }
    }

    /**
     * Запазва настройките за специфичен куриер (за подстраниците)
     *
     * @return void
     */
    public function saveSpecificCourierSettings() {
        $json = [];

        try {
            if (!$this->isPostRequest()) {
                $json['error'] = 'Невалидна заявка';
                $this->setJSONResponseOutput($json);
                return;
            }

            $this->loadModelAs('setting/delivery', 'deliveryModel');

            $method_code = $this->requestPost('method_code');
            $config_data = $this->requestPost();

            if (!$method_code) {
                $json['error'] = 'Липсва код на куриера';
                $this->setJSONResponseOutput($json);
                return;
            }

            // Подготовка на данните за запазване
            $update_data = [];
            $settings = [];

            // Основни полета които се записват в основната таблица
            $basic_fields = ['pricing_type', 'fixed_price', 'logo_path'];

            foreach ($basic_fields as $field) {
                if (isset($config_data[$field])) {
                    $update_data[$field] = $config_data[$field];
                }
            }

            // Подготовка на настройките за куриера в JSON формат
            $config_fields = $this->deliveryModel->getCourierConfigFields($method_code);

            // Събиране само на специфичните полета за куриера (без основните полета)
            foreach ($config_fields as $field_name => $field_config) {
                // Пропускаме основните полета - те се записват в основната таблица
                if (in_array($field_name, $basic_fields)) {
                    continue;
                }

                if (isset($config_data[$field_name])) {
                    // Специална обработка за checkbox полета
                    if ($field_config['type'] === 'checkbox') {
                        $settings[$field_name] = ($config_data[$field_name] === '1' || $config_data[$field_name] === 1 || $config_data[$field_name] === true);
                    } else {
                        $settings[$field_name] = $config_data[$field_name];
                    }
                } else {
                    // За checkbox полета, ако не са изпратени, задаваме false
                    if (isset($field_config['type']) && $field_config['type'] === 'checkbox') {
                        $settings[$field_name] = false;
                    }
                }
            }

            // Валидация на настройките
            if (!empty($settings)) {
                $validation_errors = $this->deliveryModel->validateCourierSettings($method_code, $settings);
                if (!empty($validation_errors)) {
                    $json['error'] = implode(', ', $validation_errors);
                    $this->setJSONResponseOutput($json);
                    return;
                }

                // Запазване на настройките чрез новия метод
                $settings_result = $this->deliveryModel->saveCourierSettings($method_code, $settings);
                if (!$settings_result) {
                    $json['error'] = 'Грешка при запазване на настройките за куриера';
                    $this->setJSONResponseOutput($json);
                    return;
                }
            }

            // Запазване на основните данни
            if (!empty($update_data)) {
                $result = $this->deliveryModel->updateDeliveryMethod($method_code, $update_data);
                if (!$result) {
                    $json['error'] = 'Грешка при запазване на основните настройки';
                    $this->setJSONResponseOutput($json);
                    return;
                }
            }

            $json['success'] = 'Конфигурацията е запазена успешно';

        } catch (Exception $e) {
            $json['error'] = 'Грешка при запазване: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Тества връзката с куриерския сървър
     *
     * @return array
     */
    public function testDeliveryConnection() {
        try {
            $this->loadModelAs('setting/delivery', 'deliveryModel');
            $code = $this->requestPost('courier_code');
            if (!$code) {
                return ['error' => 'Липсва код на куриера'];
            }

            // Получаваме настройките от заявката
            $settings = [];
            $postData = $this->requestPost();

            // Филтрираме само полетата, които са свързани с куриера
            foreach ($postData as $key => $value) {
                if (strpos($key, $code . '_') === 0 || in_array($key, ['username', 'password', 'api_key', 'merchant_id', 'client_id', 'client_secret', 'api_url', 'timeout'])) {
                    $settings[$key] = $value;
                }
            }

            $result = $this->deliveryModel->testDeliveryConnection($code, $settings);

            return $result;

        } catch (Exception $e) {
            return ['error' => 'Грешка при тестване: ' . $e->getMessage()];
        }
    }

    /**
     * Активира/деактивира метод за доставка
     *
     * @return array
     */
    public function toggleDeliveryMethod() {
        try {
            $this->loadModelAs('setting/delivery', 'deliveryModel');

            $code = $this->requestPost('code');
            $status = $this->requestPost('status', 0);

            if (!$code) {
                return ['error' => 'Липсва код на куриера'];
            }

            $result = $this->deliveryModel->updateDeliveryMethod($code, ['status' => (int)$status]);

            if ($result) {
                return ['success' => 'Методът за доставка е актуализиран успешно'];
            } else {
                return ['error' => 'Грешка при актуализиране на метода'];
            }

        } catch (Exception $e) {
            return ['error' => 'Грешка при актуализиране: ' . $e->getMessage()];
        }
    }

    /**
     * Запазва конфигурацията на метод за доставка
     *
     * @return array
     */
    public function saveDeliveryMethodConfig() {
        try {
            $this->loadModelAs('setting/delivery', 'deliveryModel');

            $code = $this->requestPost('method_code');
            $config_data = $this->requestPost();

            if (!$code) {
                return ['error' => 'Липсва код на куриера'];
            }



            // Подготовка на данните за запазване
            $update_data = [];
            $settings = [];

            // Основни полета които се записват в основната таблица
            $basic_fields = ['pricing_type', 'fixed_price', 'logo_path'];

            foreach ($basic_fields as $field) {
                if (isset($config_data[$field])) {
                    $update_data[$field] = $config_data[$field];
                }
            }

            // Подготовка на настройките за куриера в JSON формат
            $config_fields = $this->deliveryModel->getCourierConfigFields($code);

            // Събиране само на специфичните полета за куриера (без основните полета)
            foreach ($config_fields as $field_name => $field_config) {
                // Пропускаме основните полета - те се записват в основната таблица
                if (in_array($field_name, $basic_fields)) {
                    continue;
                }

                if (isset($config_data[$field_name])) {
                    // Специална обработка за checkbox полета
                    if ($field_config['type'] === 'checkbox') {
                        $settings[$field_name] = ($config_data[$field_name] === '1' || $config_data[$field_name] === 1 || $config_data[$field_name] === true);
                    } else {
                        $settings[$field_name] = $config_data[$field_name];
                    }
                } else {
                    // За checkbox полета, ако не са изпратени, задаваме false
                    if (isset($field_config['type']) && $field_config['type'] === 'checkbox') {
                        $settings[$field_name] = false;
                    }
                }
            }

            // Валидация на настройките
            if (!empty($settings)) {
                $validation_errors = $this->deliveryModel->validateCourierSettings($code, $settings);
                if (!empty($validation_errors)) {
                    return ['error' => implode(', ', $validation_errors)];
                }

                // Запазване на настройките чрез новия метод
                $settings_result = $this->deliveryModel->saveCourierSettings($code, $settings);
                if (!$settings_result) {
                    return ['error' => 'Грешка при запазване на настройките за куриера'];
                }
            }

            // Запазване на основните данни
            if (!empty($update_data)) {
                $result = $this->deliveryModel->updateDeliveryMethod($code, $update_data);
                if (!$result) {
                    return ['error' => 'Грешка при запазване на основните настройки'];
                }
            }

            return ['success' => 'Конфигурацията е запазена успешно'];

        } catch (Exception $e) {
            return ['error' => 'Грешка при запазване: ' . $e->getMessage()];
        }
    }

    /**
     * Конфигурира метод за доставка (AJAX метод)
     *
     * @return void
     */
    public function configureDeliveryMethod() {
        $json = [];
        try {
            $method_code = $this->requestGet('method');
            if (empty($method_code)) {
                $json['error'] = 'Не е посочен метод за доставка';
                $this->setJSONResponseOutput($json);
                return;
            }

            $this->loadModelAs('setting/delivery', 'deliveryModel');

            // Получаване на конфигурационните полета за метода
            $config_fields = $this->deliveryModel->getCourierConfigFields($method_code);

            if (empty($config_fields)) {
                $json['error'] = 'Няма налични настройки за този метод';
                $this->setJSONResponseOutput($json);
                return;
            }



            // Получаване на текущите настройки
            $method_data = $this->deliveryModel->getDeliveryMethod($method_code);


            // Получаваме стандартните настройки
            $default_settings = $this->deliveryModel->getCourierDefaultSettings($method_code);

            // Получаваме текущите настройки от базата данни
            $database_settings = $this->deliveryModel->getCourierSettings($method_code);

            // Merge-ваме стандартните настройки с реалните настройки от базата данни
            $current_settings = array_merge($default_settings, $database_settings);

            // Винаги добавяме основните настройки от основната таблица
            $current_settings['pricing_type'] = $method_data['pricing_type'] ?? 'calculated';
            $current_settings['fixed_price'] = $method_data['fixed_price'] ?? '0';







            $json['success'] = true;
            $json['method_code'] = $method_code;
            $json['method_name'] = $method_data['name'] ?? ucfirst($method_code);
            $json['config_fields'] = $config_fields;
            $json['current_settings'] = $current_settings;
            $json['can_test_connection'] = $this->deliveryModel->canTestCourierConnection($method_code, $current_settings);



        } catch (Exception $e) {
            error_log('Грешка при конфигуриране на метод за доставка: ' . $e->getMessage());
            $json['error'] = 'Възникна грешка при зареждането на конфигурацията';
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Проверява дали може да се тества връзката с куриера (AJAX метод)
     *
     * @return void
     */
    public function checkCanTestConnection() {
        $json = [];
        try {
            $method_code = $this->requestPost('method_code');
            $settings = $this->requestPost();

            if (empty($method_code)) {
                $json['error'] = 'Не е посочен метод за доставка';
                $this->setJSONResponseOutput($json);
                return;
            }

            $this->loadModelAs('setting/delivery', 'deliveryModel');

            $json['success'] = true;
            $json['can_test_connection'] = $this->deliveryModel->canTestCourierConnection($method_code, $settings);

        } catch (Exception $e) {
            $json['error'] = 'Грешка при проверка: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Получава конфигурацията за JavaScript модула
     *
     * @return array
     */
    public function getJavaScriptConfig() {
        return [
            'userToken' => $this->getUserToken(),
            'urls' => [
                'save' => $this->getData('save_url'),
                'testConnection' => $this->getData('test_connection_url'),
                'test_shipping' => $this->getData('test_shipping_url'),
                'toggleMethod' => $this->getData('toggle_method_url'),
                'configureMethod' => $this->getData('configure_method_url'),
                'saveMethodConfig' => $this->getData('save_method_config_url'),

                // URL-и за суб-контролерите
                'econtConfig' => $this->getData('econt_config_url'),
                'econtSave' => $this->getData('econt_save_url'),
                'speedyConfig' => $this->getData('speedy_config_url'),
                'speedySave' => $this->getData('speedy_save_url'),
                'boxnowConfig' => $this->getData('boxnow_config_url'),
                'boxnowSave' => $this->getData('boxnow_save_url'),
                'samedayConfig' => $this->getData('sameday_config_url'),
                'samedaySave' => $this->getData('sameday_save_url'),
                'pickupConfig' => $this->getData('pickup_config_url'),
                'pickupSave' => $this->getData('pickup_save_url')
            ],
            'ajaxUrls' => [
                'save' => $this->getData('save_url'),
                'testConnection' => $this->getData('test_connection_url'),
                'test_shipping' => $this->getData('test_shipping_url'),
                'toggleMethod' => $this->getData('toggle_method_url'),
                'configureMethod' => $this->getData('configure_method_url'),
                'saveMethodConfig' => $this->getData('save_method_config_url')
            ],
            'pricingTypes' => $this->getData('pricing_types'),
            'deliveryMethods' => $this->getData('delivery_methods')
        ];
    }

    /**
     * Организира полетата в табове за по-добра навигация
     *
     * @param array $config_fields Конфигурационни полета
     * @return array Организирани табове
     */
    private function organizeFieldsIntoTabs($config_fields) {
        $tabs = [];
        $current_tab = 'general';

        foreach ($config_fields as $field_name => $field_config) {
            // Проверяваме дали полето е таб
            if (isset($field_config['type']) && $field_config['type'] === 'tab') {
                $current_tab = $field_name;
                $tabs[$current_tab] = [
                    'label' => $field_config['label'],
                    'fields' => []
                ];
            } else {
                // Ако няма дефиниран таб, добавяме в общия таб
                if (!isset($tabs[$current_tab])) {
                    $tabs[$current_tab] = [
                        'label' => 'Общи настройки',
                        'fields' => []
                    ];
                }

                $tabs[$current_tab]['fields'][$field_name] = $field_config;
            }
        }

        return $tabs;
    }

    /**
     * Обработва полетата с options_source и зарежда опциите
     *
     * @param array $config_fields Конфигурационните полета
     * @param string $method_code Кода на куриера
     * @return array Обработените конфигурационни полета
     */
    private function processOptionsSourceFields($config_fields, $method_code, array $current_settings = [])
    {
        foreach ($config_fields as $field_name => &$field_config) {
            // Обработка на групи полета
            if ($field_config['type'] === 'group' && isset($field_config['subfields'])) {
                $field_config['subfields'] = $this->processOptionsSourceFields($field_config['subfields'], $method_code, $current_settings);
            }
            // Обработка на select_group полета
            elseif ($field_config['type'] === 'select_group' && isset($field_config['subfields'])) {
                $field_config['subfields'] = $this->processOptionsSourceFields($field_config['subfields'], $method_code, $current_settings);
            }
            // Обработка на полета с options_source
            elseif (isset($field_config['options_source'])) {
                $options = $this->loadOptionsFromSource($field_config['options_source'], $method_code);
                if ($options !== null) {
                    $field_config['options'] = $options;
                }
            }
            // Обработка на custom полета
            elseif (($field_config['type'] ?? null) === 'custom' && isset($field_config['source'])) {
                $customContent = $this->loadCustomFieldContent($field_config['source'], $method_code, $current_settings);
                if ($customContent !== null) {
                    $field_config['custom_content'] = $customContent;
                }
            }
        }

        return $config_fields;
    }

    /**
     * Зарежда опции от source метод
     *
     * @param string $source Source метода (напр. 'Theme25\Delivery\Speedy::getAllowedMethodsOptions')
     * @param string $method_code Кода на куриера
     * @return array|null Опциите или null при грешка
     */
    private function loadOptionsFromSource($source, $method_code)
    {
        try {
            // Парсиране на source стринга
            $parts = explode('::', $source);
            if (count($parts) !== 2) {
                return null;
            }

            $className = $parts[0];
            $methodPart = $parts[1];

            // Проверка за допълнителни параметри (напр. 'getAllowedMethods|checkbox')
            $methodParams = explode('|', $methodPart);
            $methodName = $methodParams[0];
            $type = isset($methodParams[1]) ? $methodParams[1] : null;

            // Проверка дали класът съществува
            if (!class_exists($className)) {
                return null;
            }

            // Проверка дали методът съществува
            if (!is_callable([$className, $methodName])) {
                return null;
            }

            // Извикване на метода
            if ($type) {
                $options = $className::$methodName($type);
            } else {
                $options = $className::$methodName();
            }

            return is_array($options) ? $options : null;

        } catch (Exception $e) {
            // Логиране на грешката
            error_log("Грешка при зареждане на опции от source '{$source}': " . $e->getMessage());
            return null;
        }
    }

    /**
     * Зарежда съдържанието за custom поле
     *
     * @param string $source Source метода
     * @param string $method_code Кода на куриера
     * @return string|null HTML съдържанието или null при грешка
     */
    private function loadCustomFieldContent($source, $method_code, array $current_settings = [])
    {
        try {
            // Парсиране на source стринга
            $parts = explode('::', $source);
            if (count($parts) !== 2) {
                return null;
            }

            $className = $parts[0];
            $methodName = $parts[1];

            // Проверка дали класът съществува
            if (!class_exists($className)) {
                return null;
            }

            // Проверка дали методът е извикваем (статичен) и какви параметри приема
            if (!is_callable([$className, $methodName])) {
                return null;
            }

            // Подаваме текущите настройки, ако методът ги изисква
            $ref = new \ReflectionMethod($className, $methodName);
            $params = $ref->getParameters();
            if (count($params) >= 1) {
                // Първи параметър – подаваме $current_settings
                $content = $className::$methodName($current_settings);
            } else {
                // Без параметри
                $content = $className::$methodName();
            }

            return is_string($content) ? $content : null;

        } catch (Exception $e) {
            // Логиране на грешката
            error_log("Грешка при зареждане на custom съдържание от source '{$source}': " . $e->getMessage());
            return null;
        }
    }

    /**
     * Тества връзката с куриерския API (AJAX метод)
     *
     * @return void
     */
    public function testCourierConnection() {
        $json = [];

        try {
            // Проверка за POST заявка
            if (!$this->isPostRequest()) {
                $json['error'] = 'Невалидна заявка';
                $this->setJSONResponseOutput($json);
                return;
            }

            // Получаване на параметрите от заявката
            $action = $this->requestPost('action');
            $courierCode = null;

            F()->log->developer($this->requestPost(), __FILE__, __LINE__);

            // Определяне на кода на куриера от различни възможни параметри
            if ($this->requestPost('method_code')) {
                $courierCode = $this->requestPost('method_code');
            } elseif ($this->requestPost('courier_code')) {
                $courierCode = $this->requestPost('courier_code');
            } elseif ($this->requestPost('code')) {
                $courierCode = $this->requestPost('code');
            }

            F()->log->developer($courierCode, __FILE__, __LINE__);

            // Проверка дали действието е за тестване на връзка
            if ($action !== 'test_connection' && $action !== 'test_courier_connection') {
                $json['error'] = 'Невалидно действие';
                $this->setJSONResponseOutput($json);
                return;
            }

            if (empty($courierCode)) {
                $json['error'] = 'Не е посочен код на куриера';
                $this->setJSONResponseOutput($json);
                return;
            }

            // Извикване на метода за тестване
            $result = $this->performCourierConnectionTest($courierCode);
            $json = $result;

        } catch (Exception $e) {
            error_log('Error testing courier connection: ' . $e->getMessage());
            $json['error'] = 'Възникна грешка при тестването на връзката: ' . $e->getMessage();
        }

        $this->setJSONResponseOutput($json);
    }

    /**
     * Извършва тестването на връзката с куриера
     *
     * @param string $courierCode Код на куриера
     * @return array Резултат от тестването
     */
    private function performCourierConnectionTest($courierCode) {
        try {
            // Нормализиране на кода на куриера
            $courierCode = strtolower(trim($courierCode));

            // Определяне на класа на куриера
            $courierClassName = $this->getCourierClassName($courierCode);

            if (!$courierClassName) {
                return ['error' => 'Неподдържан куриер: ' . $courierCode];
            }

            // Проверка дали класът съществува
            if (!class_exists($courierClassName)) {
                return ['error' => 'Класът на куриера не е намерен: ' . $courierClassName];
            }

            // Получаване на настройките за куриера от формата
            $courierSettings = $this->extractCourierSettingsFromRequest($courierCode);

            // Проверка дали куриерът поддържа тестване на връзка
            if (!method_exists($courierClassName, 'testConnection')) {
                return ['error' => 'Куриерът ' . $courierCode . ' не поддържа тестване на връзка'];
            }

            // Извикване на метода за тестване
            $testResult = $courierClassName::testConnection($courierSettings);

            // Обработка на резултата
            if (is_bool($testResult)) {
                if ($testResult) {
                    return ['success' => true, 'message' => 'Връзката с ' . $courierCode . ' API е успешна'];
                } else {
                    return ['error' => 'Неуспешно тестване на връзката с ' . $courierCode . ' API'];
                }
            } elseif (is_array($testResult)) {
                // Ако резултатът е масив, връщаме го директно
                return $testResult;
            } else {
                return ['error' => 'Неочакван формат на отговора от куриера'];
            }

        } catch (Exception $e) {
            error_log('Error in performCourierConnectionTest: ' . $e->getMessage());
            return ['error' => 'Грешка при тестването: ' . $e->getMessage()];
        }
    }

    /**
     * Получава името на класа на куриера въз основа на кода
     *
     * @param string $courierCode
     * @return string|null
     */
    private function getCourierClassName($courierCode) {
        // Мапинг на кодове към класове
        $courierClasses = [
            'speedy' => '\\Theme25\\Delivery\\Speedy',
            'boxnow' => '\\Theme25\\Delivery\\Boxnow',
            'econt' => '\\Theme25\\Delivery\\Econt',
            'sameday' => '\\Theme25\\Delivery\\Sameday',
            'pickup' => '\\Theme25\\Delivery\\Pickup'
        ];

        return $courierClasses[$courierCode] ?? null;
    }

    /**
     * Извлича настройките за куриера от POST заявката
     *
     * @param string $courierCode
     * @return array
     */
    private function extractCourierSettingsFromRequest($courierCode) {
        $settings = [];
        $postData = $this->requestPost();

        F()->log->developer($postData, __FILE__, __LINE__);

        // Извличане на настройки с префикс на куриера
        // $prefix = $courierCode . '_';
        // foreach ($postData as $key => $value) {
            // if (strpos($key, $prefix) === 0) {
                // Премахване на префикса от ключа
                // $settingKey = substr($key, strlen($prefix));
                // $settings[$settingKey] = $value;
            // }
        // }

        // Ако няма настройки с префикс, опитваме се да извлечем общи настройки
        if (empty($settings)) {
            $commonFields = ['username', 'password', 'api_url', 'api_key', 'merchant_id', 'client_id', 'client_secret', 'test_mode'];
            foreach ($commonFields as $field) {
                if (isset($postData[$field])) {
                    $settings[$field] = $postData[$field];
                }
            }
        }

        F()->log->developer($settings, __FILE__, __LINE__);

        return $settings;
    }
}
