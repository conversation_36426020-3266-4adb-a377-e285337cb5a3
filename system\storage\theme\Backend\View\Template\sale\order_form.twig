<!-- Order Form Header -->
<div class="bg-white border-b border-gray-200 px-6 py-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">Редакция на поръчка #{{ order_id }}</h1>
            <p class="text-gray-500 mt-1">Редактиране на информацията за поръчката</p>
        </div>
        <div class="mt-4 md:mt-0 flex space-x-2">
            {% if back_url %}
            <a href="{{ back_url }}" class="px-4 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 transition-colors whitespace-nowrap flex items-center">
                <div class="w-5 h-5 flex items-center justify-center mr-2">
                    <i class="ri-arrow-left-line"></i>
                </div>
                <span>Назад</span>
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- Main Content Area -->
<main class="flex-1 overflow-y-auto bg-gray-50 p-6">
    <form id="order-edit-form" method="post" action="{{ save_url }}" class="max-w-7xl space-y-6">
        <input type="hidden" name="user_token" value="{{ user_token }}">
        <input type="hidden" name="order_id" value="{{ order_id }}">
        
        <!-- Customer Information -->
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Информация за клиента</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Име *</label>
                        <input type="text" name="firstname" value="{{ firstname }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Фамилия *</label>
                        <input type="text" name="lastname" value="{{ lastname }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Имейл *</label>
                        <input type="email" name="email" value="{{ email }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Телефон</label>
                        <input type="text" name="telephone" value="{{ telephone }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Address -->
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Адрес за плащане</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Име</label>
                        <input type="text" name="payment_firstname" value="{{ payment_firstname }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Фамилия</label>
                        <input type="text" name="payment_lastname" value="{{ payment_lastname }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Фирма</label>
                        <input type="text" name="payment_company" value="{{ payment_company }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Адрес 1</label>
                        <input type="text" name="payment_address_1" value="{{ payment_address_1 }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Адрес 2</label>
                        <input type="text" name="payment_address_2" value="{{ payment_address_2 }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Град</label>
                        <input type="text" name="payment_city" value="{{ payment_city }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Пощенски код</label>
                        <input type="text" name="payment_postcode" value="{{ payment_postcode }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                </div>
            </div>
        </div>

        <!-- Shipping Address -->
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Адрес за доставка</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Име</label>
                        <input type="text" name="shipping_firstname" value="{{ shipping_firstname }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Фамилия</label>
                        <input type="text" name="shipping_lastname" value="{{ shipping_lastname }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Фирма</label>
                        <input type="text" name="shipping_company" value="{{ shipping_company }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Адрес 1</label>
                        <input type="text" name="shipping_address_1" value="{{ shipping_address_1 }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Адрес 2</label>
                        <input type="text" name="shipping_address_2" value="{{ shipping_address_2 }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Град</label>
                        <input type="text" name="shipping_city" value="{{ shipping_city }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Пощенски код</label>
                        <input type="text" name="shipping_postcode" value="{{ shipping_postcode }}" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Products -->
        {% if order_products %}
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-800">Продукти в поръчката</h3>
                <button type="button" id="add-product-btn" class="px-4 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-add-line"></i>
                    </div>
                    <span>Добави продукт</span>
                </button>
            </div>
            <div class="overflow-x-auto">
                <table id="order-products-table" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Продукт
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Количество
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Ед. цена
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Сума
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Действия
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for product in order_products %}
                        <tr data-product-row="{{ product.order_product_id }}">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    {% if product.image %}
                                    <div class="flex-shrink-0 h-16 w-16 bg-gray-100 rounded">
                                        <img class="h-16 w-16 rounded object-cover object-top" src="{{ product.image }}" alt="{{ product.name }}">
                                    </div>
                                    {% endif %}
                                    <div class="{% if product.image %}ml-4{% endif %}">
                                        <div class="text-sm font-medium text-gray-900">{{ product.name }}</div>
                                        {% if product.model %}
                                        <div class="text-sm text-gray-500">Модел: {{ product.model }}</div>
                                        {% endif %}
                                        {% if product.options %}
                                            {% for option in product.options %}
                                            <div class="text-xs text-gray-500">{{ option.name }}: {{ option.value }}</div>
                                            {% endfor %}
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-center">
                                <input type="number" name="products[{{ product.order_product_id }}][quantity]" value="{{ product.quantity }}" min="1" class="product-quantity w-20 px-2 py-1 border border-gray-300 rounded text-center text-sm" data-product-id="{{ product.composite_product_id|default(product.order_product_id) }}">
                            </td>
                            <td class="px-6 py-4 text-right">
                                <input type="number" name="products[{{ product.order_product_id }}][price]" value="{{ product.price|number_format(2, ".", "") }}" step="0.01" class="product-price w-24 px-2 py-1 border border-gray-300 rounded text-right text-sm" data-product-id="{{ product.composite_product_id|default(product.order_product_id) }}">
                            </td>
                            <td class="px-6 py-4 text-right text-sm font-medium text-gray-900 product-total" data-product-id="{{ product.composite_product_id|default(product.order_product_id) }}">
                                {{ product.total|number_format(2, ".", " ") }}
                            </td>
                            <td class="px-6 py-4 text-center">
                                <button type="button" class="remove-product-btn text-red-500 hover:text-red-700 transition-colors" data-product-id="{{ product.composite_product_id|default(product.order_product_id) }}">
                                    <div class="w-5 h-5 flex items-center justify-center">
                                        <i class="ri-delete-bin-line"></i>
                                    </div>
                                </button>

                                <!-- Hidden fields for product data -->
                                <!-- Реален order_product_id за връзка с БД -->
                                <input type="hidden" name="products[{{ product.order_product_id }}][real_order_product_id]" value="{{ product.order_product_id }}">
                                <!-- Композитен ID за JavaScript операции -->
                                <input type="hidden" name="products[{{ product.order_product_id }}][composite_product_id]" value="{{ product.composite_product_id|default(product.order_product_id) }}">
                                <input type="hidden" name="products[{{ product.order_product_id }}][product_id]" value="{{ product.product_id }}">
                                <input type="hidden" name="products[{{ product.order_product_id }}][name]" value="{{ product.name }}">
                                {% if product.model %}
                                <input type="hidden" name="products[{{ product.order_product_id }}][model]" value="{{ product.model }}">
                                {% endif %}

                                <!-- Hidden fields for product options -->
                                {% if product.options %}
                                    {% for option in product.options %}
                                    <input type="hidden" name="products[{{ product.order_product_id }}][options][{{ loop.index0 }}][product_option_id]" value="{{ option.product_option_id }}">
                                    <input type="hidden" name="products[{{ product.order_product_id }}][options][{{ loop.index0 }}][product_option_value_id]" value="{{ option.product_option_value_id }}">
                                    <input type="hidden" name="products[{{ product.order_product_id }}][options][{{ loop.index0 }}][name]" value="{{ option.name }}">
                                    <input type="hidden" name="products[{{ product.order_product_id }}][options][{{ loop.index0 }}][value]" value="{{ option.value }}">
                                    {% if option.option_value_id %}
                                    <input type="hidden" name="products[{{ product.order_product_id }}][options][{{ loop.index0 }}][option_value_id]" value="{{ option.option_value_id }}">
                                    {% endif %}
                                    {% endfor %}
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% endif %}

        <!-- Discounts Section -->
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <div class="w-5 h-5 flex items-center justify-center mr-2">
                        <i class="ri-coupon-line"></i>
                    </div>
                    Отстъпки
                </h3>
            </div>
            <div class="p-6">
                {% set has_voucher = voucher_code is defined and voucher_code %}
                {% set has_coupon  = coupon_code is defined and coupon_code %}
                {% set voucher_text = '' %}
                {% set coupon_text  = '' %}
                {% if order_totals %}
                    {% for total in order_totals %}
                        {% if total.code == 'voucher' %}{% set voucher_text = total.text %}{% endif %}
                        {% if total.code == 'coupon' %}{% set coupon_text = total.text %}{% endif %}
                    {% endfor %}
                {% endif %}

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="w-4 h-4 flex items-center justify-center mr-2">
                                <i class="ri-gift-line"></i>
                            </div>
                            Ваучер код
                        </label>
                        <div id="voucher-input-wrap" class="flex space-x-2{% if has_voucher %} hidden{% endif %}">
                            <input type="text" id="voucher-code-input" name="voucher" value="{{ voucher_code }}" class="flex-1 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" placeholder="Въведете ваучер код">
                            <button type="button" id="apply-voucher-btn" class="px-4 py-2 bg-green-600 text-white rounded-button hover:bg-green-700 transition-colors flex items-center whitespace-nowrap">
                                <div class="w-4 h-4 flex items-center justify-center mr-2">
                                    <i class="ri-check-line"></i>
                                </div>
                                Приложи
                            </button>
                        </div>
                        <div id="voucher-card-wrap" class="{% if not has_voucher %}hidden {% endif %}">
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-5 h-5 flex items-center justify-center mr-3 text-green-600"><i class="ri-gift-line"></i></div>
                                    <div>
                                        <div class="text-sm font-medium text-green-800">Приложен ваучер: {{ voucher_code }}</div>
                                        {% if voucher_text %}<div class="text-xs text-green-700">Стойност: {{ voucher_text }}</div>{% endif %}
                                        <input type="hidden" name="voucher" value="{{ voucher_code }}">
                                    </div>
                                </div>
                                <button type="button" id="remove-voucher-btn" onclick="BackendModule.removeVoucher()" class="px-3 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 transition-colors flex items-center">
                                    <i class="ri-close-line mr-2"></i> Премахни
                                </button>
                            </div>
                        </div>
                        <div id="voucher-message" class="mt-2 text-sm hidden"></div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                            <div class="w-4 h-4 flex items-center justify-center mr-2">
                                <i class="ri-ticket-line"></i>
                            </div>
                            Купон код
                        </label>
                        <div id="coupon-input-wrap" class="flex space-x-2{% if has_coupon %} hidden{% endif %}">
                            <input type="text" id="coupon-code-input" name="coupon" value="{{ coupon_code }}" class="flex-1 px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary" placeholder="Въведете купон код">
                            <button type="button" id="apply-coupon-btn" class="px-4 py-2 bg-blue-600 text-white rounded-button hover:bg-blue-700 transition-colors flex items-center whitespace-nowrap">
                                <div class="w-4 h-4 flex items-center justify-center mr-2">
                                    <i class="ri-check-line"></i>
                                </div>
                                Приложи
                            </button>
                        </div>
                        <div id="coupon-card-wrap" class="{% if not has_coupon %}hidden {% endif %}">
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-5 h-5 flex items-center justify-center mr-3 text-blue-600"><i class="ri-ticket-line"></i></div>
                                    <div>
                                        <div class="text-sm font-medium text-blue-800">Приложен купон: {{ coupon_code }}</div>
                                        {% if coupon_text %}<div class="text-xs text-blue-700">Стойност: {{ coupon_text }}</div>{%else%}<div class="text-xs text-blue-700"></div>{% endif %}
                                        <input type="hidden" name="coupon" value="{{ coupon_code }}">
                                    </div>
                                </div>
                                <button type="button" id="remove-coupon-btn" onclick="BackendModule.removeCoupon()" class="px-3 py-2 bg-red-600 text-white rounded-button hover:bg-red-700 transition-colors flex items-center">
                                    <i class="ri-close-line mr-2"></i> Премахни
                                </button>
                            </div>
                        </div>
                        <div id="coupon-message" class="mt-2 text-sm hidden"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Order Totals -->
        {% if order_totals %}
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Общи суми</h3>
            </div>
            <div class="p-6">
                <div class="space-y-2" id="order-totals-container">
                    {% for total in order_totals %}
                    <div class="flex justify-between{% if total.code == 'total' %} border-t border-gray-200 pt-2 mt-2{% endif %}" data-total-code="{{ total.code }}">
                        <span class="{% if total.code == 'total' %}font-semibold{% elseif total.code == 'coupon' %}font-semibold text-sm text-green-600{% elseif total.code == 'voucher' %}font-semibold text-sm text-green-600{% else %}font-semibold text-sm{% endif %}">{{ total.title }}:</span>
                        <span class="{% if total.code == 'total' %}text-primary font-semibold{% elseif total.code == 'coupon' %}text-green-600 text-sm font-semibold{% elseif total.code == 'voucher' %}text-green-600 text-sm font-semibold{% else %}font-semibold text-sm{% endif %}" data-total-value="{{ total.value }}">{{ total.text }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Order Settings -->
        <div class="bg-white rounded shadow">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800">Настройки на поръчката</h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {% if order_statuses %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Статус</label>
                        <select name="order_status_id" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                            {% for status in order_statuses %}
                            <option value="{{ status.order_status_id }}" {% if status.order_status_id == order_status_id %}selected{% endif %}>{{ status.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}
                    {% if payment_methods %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Метод на плащане</label>
                        <select name="payment_code" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                            {% for method in payment_methods %}
                            <option value="{{ method.code }}" {% if method.code == payment_code %}selected{% endif %}>{{ method.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}
                    {% if shipping_methods %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Метод на доставка</label>
                        <select name="shipping_code" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">
                            {% for method in shipping_methods %}
                            <option value="{{ method.code }}" {% if method.code == shipping_code %}selected{% endif %}>{{ method.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Коментар</label>
                        <textarea name="comment" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-button focus:outline-none focus:border-primary">{{ comment }}</textarea>
                    </div>
                </div>
                
                <div class="mt-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="notify" value="1" class="mr-2">
                        <span class="text-sm text-gray-700">Изпрати известие до клиента при промяна на статуса</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="bg-white rounded shadow overflow-hidden">
            <div class="px-6 py-4 flex justify-end space-x-4">
                {% if back_url %}
                <a href="{{ back_url }}" class="px-6 py-2 border border-gray-300 rounded-button text-gray-700 hover:bg-gray-50 transition-colors">
                    Отказ
                </a>
                {% endif %}
                <button type="submit" class="px-6 py-2 bg-primary text-white rounded-button hover:bg-primary/90 transition-colors">
                    Запази промените
                </button>
            </div>
        </div>

    </form>
</main>
