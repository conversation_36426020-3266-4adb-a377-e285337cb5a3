<?php

namespace Theme25\Model\Setting;

/**
 * Модел за настройки на доставка
 * Управлява куриерските услуги и техните настройки
 */
class Delivery extends \Theme25\Model {

    private $initialized = false;

    /**
     * Конструктор
     */
    public function __construct($registry) {
        parent::__construct($registry);


        F()->log->developer('>>> Delivery model', __FILE__, __LINE__);

        $this->init();
    }

    public function init() {
        $this->db = $this->getFirstDatabase();
        $this->disableAutoSwitch();

        $this->initialized = true;

        F()->log->developer('Delivery model initialized', __FILE__, __LINE__);

        // $this->createDeliveryMethodsTable();
    }

    /**
     * Създава таблицата за методи на доставка ако не съществува
     */
    private function createDeliveryMethodsTable() {
        $sql = "CREATE TABLE IF NOT EXISTS `" . DB_PREFIX . "delivery_methods` (
            `delivery_method_id` int(11) NOT NULL AUTO_INCREMENT,
            `code` varchar(32) NOT NULL,
            `name` varchar(255) NOT NULL,
            `description` text,
            `status` tinyint(1) NOT NULL DEFAULT '0',
            `pricing_type` enum('fixed','free','calculated') NOT NULL DEFAULT 'fixed',
            `fixed_price` decimal(10,2) DEFAULT '0.00',
            `logo_path` varchar(500) DEFAULT NULL,
            `settings` text,
            `sort_order` int(3) NOT NULL DEFAULT '0',
            `date_added` datetime NOT NULL,
            `date_modified` datetime NOT NULL,
            PRIMARY KEY (`delivery_method_id`),
            UNIQUE KEY `code` (`code`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        $this->dbQuery($sql);

        // Добавяне на начални данни ако таблицата е празна
        $this->insertDefaultDeliveryMethods();
    }

    public function checkInitialized() {
        $this->init();
    }

    /**
     * Добавя начални методи за доставка
     */
    private function insertDefaultDeliveryMethods() {
        $this->checkInitialized();
        $count_query = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "delivery_methods`");
        
        $codes = [];
        foreach ($count_query->rows as $row) {
            $codes[] = $row['code'];
        }

        $default_methods = [
            [
                'code' => 'econt',
                'name' => 'Еконт Експрес',
                'description' => 'Доставка чрез Еконт Експрес',
                'status' => 0,
                'pricing_type' => 'calculated',
                'sort_order' => 1
            ],
            [
                'code' => 'speedy',
                'name' => 'Спиди',
                'description' => 'Доставка чрез Спиди',
                'status' => 0,
                'pricing_type' => 'calculated',
                'sort_order' => 2
            ],
            [
                'code' => 'boxnow',
                'name' => 'BoxNow',
                'description' => 'Доставка чрез BoxNow автомати',
                'status' => 0,
                'pricing_type' => 'calculated',
                'sort_order' => 3
            ],
            [
                'code' => 'sameday',
                'name' => 'Sameday',
                'description' => 'Доставка чрез Sameday',
                'status' => 0,
                'pricing_type' => 'calculated',
                'sort_order' => 4
            ],
            [
                'code' => 'pickup',
                'name' => 'Вземане от магазина',
                'description' => 'Вземане от физическия магазин',
                'status' => 1,
                'pricing_type' => 'free',
                'sort_order' => 5
            ]
        ];

        
        foreach ($default_methods as $method) {
            if (!in_array($method['code'], $codes)) {
                $this->dbQuery("INSERT INTO `" . DB_PREFIX . "delivery_methods` SET
                    `code` = '" . $this->dbEscape($method['code']) . "',
                    `name` = '" . $this->dbEscape($method['name']) . "',
                    `description` = '" . $this->dbEscape($method['description']) . "',
                    `status` = '" . (int)$method['status'] . "',
                    `pricing_type` = '" . $this->dbEscape($method['pricing_type']) . "',
                    `sort_order` = '" . (int)$method['sort_order'] . "',
                    `date_added` = NOW(),
                    `date_modified` = NOW()");
            }
        }

      
    }

    /**
     * Получава всички методи за доставка
     *
     * @return array
     */
    public function getDeliveryMethods() {

        F()->log->developer('>>> getDeliveryMethods', __FILE__, __LINE__);
        $this->checkInitialized();
        
        $query = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "delivery_methods` ORDER BY `sort_order` ASC, `name` ASC");

        $methods = [];
        foreach ($query->rows as $row) {
            $logo_url = '';
            if (!empty($row['logo_path'])) {
                $logo_url = ThemeData()->getImageWebUrl() . $row['logo_path'];
            }

            $methods[$row['code']] = [
                'delivery_method_id' => $row['delivery_method_id'],
                'code' => $row['code'],
                'name' => $row['name'],
                'description' => $row['description'],
                'status' => (bool)$row['status'],
                'pricing_type' => $row['pricing_type'],
                'fixed_price' => $row['fixed_price'],
                'logo_path' => $row['logo_path'],
                'logo_url' => $logo_url,
                'settings' => $row['settings'] ? json_decode($row['settings'], true) : [],
                'sort_order' => $row['sort_order'],
                'date_added' => $row['date_added'],
                'date_modified' => $row['date_modified']
            ];
        }

        return $methods;
    }

    /**
     * Получава mapping между ID и кодове на куриери
     *
     * @return array
     */
    public function getDeliveryMethodIdMapping() {
        $this->checkInitialized();
        
        $query = $this->dbQuery("SELECT `delivery_method_id`, `code` FROM `" . DB_PREFIX . "delivery_methods`");

        $mapping = [
            'id_to_code' => [],
            'code_to_id' => []
        ];

        foreach ($query->rows as $row) {
            $mapping['id_to_code'][$row['delivery_method_id']] = $row['code'];
            $mapping['code_to_id'][$row['code']] = $row['delivery_method_id'];
        }

        return $mapping;
    }

    /**
     * Получава код на куриер по ID
     *
     * @param int $id
     * @return string|null
     */
    public function getDeliveryMethodCodeById($id) {
        $this->checkInitialized();
        
        $query = $this->dbQuery("SELECT `code` FROM `" . DB_PREFIX . "delivery_methods` WHERE `delivery_method_id` = '" . (int)$id . "'");

        return $query->num_rows ? $query->row['code'] : null;
    }

    /**
     * Получава ID на куриер по код
     *
     * @param string $code
     * @return int|null
     */
    public function getDeliveryMethodIdByCode($code) {
        $this->checkInitialized();
        
        $query = $this->dbQuery("SELECT `delivery_method_id` FROM `" . DB_PREFIX . "delivery_methods` WHERE `code` = '" . $this->dbEscape($code) . "'");

        return $query->num_rows ? (int)$query->row['delivery_method_id'] : null;
    }

    /**
     * Получава метод за доставка по ID
     *
     * @param int $id
     * @return array|null
     */
    public function getDeliveryMethodById($id) {
        $this->checkInitialized();
        
        $query = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "delivery_methods` WHERE `delivery_method_id` = '" . (int)$id . "'");

        if ($query->num_rows) {
            $row = $query->row;

            $logo_url = '';
            if (!empty($row['logo_path'])) {
                $logo_url = ThemeData()->getImageWebUrl() . $row['logo_path'];
            }

            return [
                'delivery_method_id' => $row['delivery_method_id'],
                'code' => $row['code'],
                'name' => $row['name'],
                'description' => $row['description'],
                'status' => (bool)$row['status'],
                'pricing_type' => $row['pricing_type'],
                'fixed_price' => $row['fixed_price'],
                'logo_path' => $row['logo_path'],
                'logo_url' => $logo_url,
                'settings' => $row['settings'], // Запазваме като raw JSON string
                'sort_order' => $row['sort_order'],
                'date_added' => $row['date_added'],
                'date_modified' => $row['date_modified']
            ];
        }

        return null;
    }

    /**
     * Актуализира метод за доставка
     *
     * @param string $code Код на метода
     * @param array $data Данни за актуализиране
     * @return bool
     */
    public function updateDeliveryMethod($code, $data) {
        $this->checkInitialized();                
        $set_data = [];

        if (isset($data['status'])) {
            $set_data[] = "`status` = '" . (int)$data['status'] . "'";
        }

        if (isset($data['pricing_type'])) {
            $set_data[] = "`pricing_type` = '" . $this->dbEscape($data['pricing_type']) . "'";
        }

        if (isset($data['fixed_price'])) {
            $set_data[] = "`fixed_price` = '" . (float)$data['fixed_price'] . "'";
        }

        if (isset($data['logo_path'])) {
            $set_data[] = "`logo_path` = '" . $this->dbEscape($data['logo_path']) . "'";
        }

        if (isset($data['settings'])) {
            $settings_json = is_array($data['settings']) ? json_encode($data['settings']) : $data['settings'];
            $set_data[] = "`settings` = '" . $this->dbEscape($settings_json) . "'";
        }

        if (isset($data['sort_order'])) {
            $set_data[] = "`sort_order` = '" . (int)$data['sort_order'] . "'";
        }

        if (!empty($set_data)) {
            $set_data[] = "`date_modified` = NOW()";
            $sql = "UPDATE `" . DB_PREFIX . "delivery_methods` SET " . implode(', ', $set_data) . " WHERE `code` = '" . $this->dbEscape($code) . "'";
            $this->dbQuery($sql);

            return true;
        }   
        return false;
    }

    /**
     * Получава метод за доставка по код
     *
     * @param string $code
     * @return array|null
     */
    public function getDeliveryMethod($code) {
        $this->checkInitialized();
        $query = $this->dbQuery("SELECT * FROM `" . DB_PREFIX . "delivery_methods` WHERE `code` = '" . $this->dbEscape($code) . "'");

        if ($query->num_rows) {
            $row = $query->row;


            $logo_url = '';
            if (!empty($row['logo_path'])) {
                $logo_url = ThemeData()->getImageWebUrl() . $row['logo_path'];
            }

            $result = [
                'delivery_method_id' => $row['delivery_method_id'],
                'code' => $row['code'],
                'name' => $row['name'],
                'description' => $row['description'],
                'status' => (bool)$row['status'],
                'pricing_type' => $row['pricing_type'],
                'fixed_price' => $row['fixed_price'],
                'logo_path' => $row['logo_path'],
                'logo_url' => $logo_url,
                'settings' => $row['settings'], // Запазваме като raw JSON string
                'sort_order' => $row['sort_order'],
                'date_added' => $row['date_added'],
                'date_modified' => $row['date_modified']
            ];

            return $result;
        }

        return null;
    }

    /**
     * Запазва настройките за всички методи за доставка
     *
     * @param array $data Данни за запазване
     * @return array Резултат с успех или грешки
     */
    public function saveAllDeliverySettings($data) {
        $this->checkInitialized();
        $errors = [];
        $success_count = 0;

        if (isset($data['delivery_methods']) && is_array($data['delivery_methods'])) {
            foreach ($data['delivery_methods'] as $code => $method_data) {
                try {
                    $this->updateDeliveryMethod($code, $method_data);
                    $success_count++;
                } catch (Exception $e) {
                    $errors[] = "Грешка при запазване на {$code}: " . $e->getMessage();
                }
            }
        }

        if (empty($errors)) {
            return ['success' => "Успешно запазени {$success_count} метода за доставка"];
        } else {
            return ['errors' => $errors];
        }
    }

    /**
     * Тества връзката с куриерския сървър динамично
     *
     * @param string $code Код на куриера
     * @param array $settings Настройки за тестване (опционално)
     * @return array Резултат от теста
     */
    public function testDeliveryConnection($code, $settings = []) {
        $this->checkInitialized();
        try {
            // Получаваме класа за куриера динамично
            $courierClass = $this->getCourierClass($code);

            if (!$courierClass) {
                return [
                    'success' => false,
                    'error' => "Не е намерен клас за куриер с код '{$code}'. Проверете дали файлът system/storage/theme/Delivery/" . ucfirst(strtolower($code)) . ".php съществува."
                ];
            }

            // Проверяваме дали класът има метод testConnection
            if (!is_callable([$courierClass, 'testConnection'])) {
                return [
                    'success' => false,
                    'error' => "Класът {$courierClass} не имплементира метод testConnection(). Моля добавете този метод в класа."
                ];
            }

            // Ако не са подадени настройки, опитваме се да ги получим от базата данни
            if (empty($settings)) {
                $method = $this->getDeliveryMethod($code);
                if ($method && isset($method['settings'])) {
                    $settings = is_string($method['settings']) ? json_decode($method['settings'], true) : $method['settings'];
                }

                if (!is_array($settings)) {
                    $settings = [];
                }
            }

            // Извикваме метода testConnection на куриерския клас
            $result = $courierClass::testConnection($settings);

            // Валидираме резултата
            if (!is_array($result)) {
                return [
                    'success' => false,
                    'error' => "Методът testConnection() на класа {$courierClass} трябва да връща масив."
                ];
            }

            // Осигуряваме консистентен формат на отговора
            if (isset($result['success']) && $result['success']) {
                return [
                    'success' => true,
                    'message' => $result['message'] ?? 'Връзката с куриера е успешна',
                    'data' => $result['data'] ?? null
                ];
            } else {
                return [
                    'success' => false,
                    'error' => $result['error'] ?? $result['message'] ?? 'Грешка при тестване на връзката с куриера'
                ];
            }

        } catch (\Exception $e) {
            // Логиране на грешката
            error_log("Delivery connection test error for courier '{$code}': " . $e->getMessage());

            return [
                'success' => false,
                'error' => 'Възникна грешка при тестване на връзката: ' . $e->getMessage()
            ];
        }
    }



    /**
     * Получава налични типове цени за доставка
     *
     * @return array
     */
    // public function getPricingTypes() {
    //     return [
    //         'fixed' => 'Фиксирана цена',
    //         'free' => 'Безплатна доставка',
    //         'calculated' => 'Изчислена от куриерския сървър'
    //     ];
    // }

    /**
     * Получава настройките за конфигуриране на куриер
     *
     * @param string $code Код на куриера
     * @return array
     */
    public function getCourierConfigFields($code) {
        // Определяне на класа за куриера
        $courierClass = $this->getCourierClass($code);

        if ($courierClass && is_callable([$courierClass, 'getConfigFields'])) {
            return $courierClass::getConfigFields();
        }

        return [];
    }

    /**
     * Получава класа за куриер по код
     *
     * @param string $code Код на куриера
     * @return string|null
     */
    private function getCourierClass($code) {
        $className = ucfirst(strtolower($code));
        $fullClassName = "\\Theme25\\Delivery\\{$className}";

        if (class_exists($fullClassName)) {
            return $fullClassName;
        }

        return null;
    }

    /**
     * Получава стандартните настройки за куриер
     *
     * @param string $code Код на куриера
     * @return array
     */
    public function getCourierDefaultSettings($code) {
        $courierClass = $this->getCourierClass($code);
        if ($courierClass && is_callable([$courierClass, 'getDefaultSettings'])) {
            return $courierClass::getDefaultSettings();
        }

        return [];
    }

    /**
     * Валидира настройките за куриер
     *
     * @param string $code Код на куриера
     * @param array $settings Настройки за валидация
     * @return array Масив с грешки (празен ако няма грешки)
     */
    public function validateCourierSettings($code, $settings) {
        $courierClass = $this->getCourierClass($code);

        if ($courierClass && is_callable([$courierClass, 'validateSettings'])) {
            return $courierClass::validateSettings($settings);
        }

        return [];
    }

    /**
     * Получава настройките за куриер от JSON колоната
     *
     * @param string $code Код на куриера
     * @return array
     */
    public function getCourierSettings($code) {
        $method = $this->getDeliveryMethod($code);

        if (!$method) {

            return [];
        }

        if (!empty($method['settings'])) {
            $settings = json_decode($method['settings'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($settings)) {
                return $settings;
            }
        }

        // Връщане на стандартните настройки ако няма запазени
        $defaultSettings = $this->getCourierDefaultSettings($code);
        return $defaultSettings;
    }

    /**
     * Запазва настройките за куриер в JSON колоната
     *
     * @param string $code Код на куриера
     * @param array $settings Настройки за запазване
     * @return bool
     */
    public function saveCourierSettings($code, $settings) {
        $this->checkInitialized();

        // Валидация на настройките
        $errors = $this->validateCourierSettings($code, $settings);
        if (!empty($errors)) {

            return false;
        }

        $settingsJson = json_encode($settings, JSON_UNESCAPED_UNICODE);


        $sql = "UPDATE `" . DB_PREFIX . "delivery_methods`
                SET `settings` = '" . $this->dbEscape($settingsJson) . "',
                    `date_modified` = NOW()
                WHERE `code` = '" . $this->dbEscape($code) . "'";

        $this->dbQuery($sql);


        return true;
    }

    /**
     * Проверява дали куриерът може да тества връзката с текущите настройки
     *
     * @param string $code Код на куриера
     * @param array $settings Настройки за проверка
     * @return bool
     */
    public function canTestCourierConnection($code, $settings) {
        $this->checkInitialized();
        $courierClass = $this->getCourierClass($code);

        if ($courierClass && is_callable([$courierClass, 'canTestConnection'])) {
            return $courierClass::canTestConnection($settings);
        }

        return false;
    }


    public function saveCourierLogo($courierCode, $logoPath) {
        $this->checkInitialized();

        $courier = $this->getDeliveryMethod($courierCode);

        if (!$courier) {
            return false;
        }

        $this->dbQuery("UPDATE `" . DB_PREFIX . "delivery_methods` SET `logo_path` = '" . $this->dbEscape($logoPath) . "' WHERE `code` = '" . $this->dbEscape($courierCode) . "'");

        return true;
        
    }



}

