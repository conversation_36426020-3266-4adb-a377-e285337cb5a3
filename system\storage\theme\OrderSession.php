<?php
namespace Theme25;

/**
 * OrderSession клас за изолация на Backend операции от Frontend сесии
 * 
 * Този клас осигурява пълна изолация между административни операции в Backend
 * и клиентски операции във Frontend чрез използване на отделна order_session таблица.
 * 
 * Основни функции:
 * - Съхранение на временни данни за Backend операции с поръчки
 * - Изолация от стандартните PHP сесии
 * - Автоматично генериране на уникални session_key-ове
 * - JSON сериализация на сложни данни
 */
class OrderSession {
    private $db;
    private $order_id;
    private $session_key;
    private $session_data = [];
    private $is_loaded = false;
    private $is_dirty = false;
    
    /**
     * Конструктор
     * 
     * @param object $db Database обект
     * @param int $order_id ID на поръчката
     * @param string $session_key Опционален session key (ако не е подаден, се генерира автоматично)
     */
    public function __construct($db, $order_id = null, $session_key = null) {
        $this->db = $db;
        $this->order_id = (int)$order_id;

        if(!$this->order_id && $this->isAjaxRequest() && isset($_POST['order_id'])) {
            $this->order_id = (int)$_POST['order_id'];
        }

        if ($session_key) {
            // Използваме подадения session key
            $this->session_key = $session_key;
        } else {
            // Генериране на постоянен session key за Backend операции
            $this->session_key = $this->generateSessionKey();

            // Проверка дали вече съществува сесия с този ключ
            if ($this->order_id > 0) {
                $existing_session = $this->findExistingSession();
                if ($existing_session) {
                    // Използваме съществуващата сесия
                    $this->session_key = $existing_session['session_key'];
                }
            }
        }


        // Автоматично зареждане на данните при инициализация
        if ($this->order_id > 0) {
            $this->loadSessionData();
        }

        // Автоматично почистване на стари сесии при инициализация (само при първо зареждане)
        if (!$this->isAjaxRequest()) {

            $this->cleanupOldSessions();
        }
    }
    
    /**
     * Генерира постоянен session key за Backend операции
     * Ключът е базиран на order_id, user_id и дата, за да е постоянен през цялата сесия
     */
    private function generateSessionKey() {
        // Получаване на user_id от глобалните променливи или сесията
        $user_id = 0;
        if (isset($_SESSION['user_id'])) {
            $user_id = (int)$_SESSION['user_id'];
        } elseif (function_exists('session_id') && session_id()) {
            // Fallback към session_id ако няма user_id
            $user_id = crc32(session_id());
        }

        // Генериране на постоянен ключ базиран на order_id, user_id и дата (без час)
        // Това гарантира, че същият ключ ще се генерира през цялата работна сесия
        $date_key = date('Y-m-d'); // Само дата, без час - постоянен за целия ден
        $unique_string = 'backend_' . $this->order_id . '_' . $user_id . '_' . $date_key;

        return md5($unique_string);
    }

    /**
     * Търси съществуваща активна сесия за този order_id
     *
     * @return array|null Данни за съществуващата сесия или null
     */
    private function findExistingSession() {
        if ($this->order_id <= 0) {
            return null;
        }

        // Търсене на активна сесия за този order_id (създадена в последните 24 часа)
        $query = $this->db->query("
            SELECT session_key, created_at
            FROM " . DB_PREFIX . "order_session
            WHERE order_id = '" . (int)$this->order_id . "'
            AND created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ORDER BY created_at DESC
            LIMIT 1
        ");

        if ($query->num_rows) {
            return $query->row;
        }

        return null;
    }

    /**
     * Проверява дали заявката е AJAX
     *
     * @return bool
     */
    private function isAjaxRequest() {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
    }

    /**
     * Зарежда сесийните данни от базата данни
     */
    private function loadSessionData() {
        if ($this->is_loaded || $this->order_id <= 0) {
            return;
        }
        
        $query = $this->db->query("
            SELECT session_data 
            FROM " . DB_PREFIX . "order_session 
            WHERE order_id = '" . (int)$this->order_id . "' 
            AND session_key = '" . $this->db->escape($this->session_key) . "'
        ");
        
        if ($query->num_rows) {
            $json_data = $query->row['session_data'];
            $this->session_data = json_decode($json_data, true) ?: [];
        } else {
            $this->session_data = [];
        }
        
        $this->is_loaded = true;
        $this->is_dirty = false;
    }
    
    /**
     * Запазва сесийните данни в базата данни
     */
    private function saveSessionData($action = '-') {

        if (!$this->is_dirty || $this->order_id <= 0) {
            return;
        }

        $json_data = json_encode($this->session_data);
        $now = date('Y-m-d H:i:s');
        
        $json_data = json_encode($this->session_data);
        $now = date('Y-m-d H:i:s');
        
        // Проверка дали записът съществува
        $exists_query = $this->db->query("
            SELECT order_session_id 
            FROM " . DB_PREFIX . "order_session 
            WHERE order_id = '" . (int)$this->order_id . "' 
            AND session_key = '" . $this->db->escape($this->session_key) . "'
        ");

        if ($exists_query->num_rows) {
            // Актуализиране на съществуващ запис
            $this->db->query("
                UPDATE " . DB_PREFIX . "order_session SET 
                session_data = '" . $this->db->escape($json_data) . "',
                updated_at = '" . $now . "'
                WHERE order_id = '" . (int)$this->order_id . "' 
                AND session_key = '" . $this->db->escape($this->session_key) . "'
            ");
        } else {
            // Създаване на нов запис
            $this->db->query("
                INSERT INTO " . DB_PREFIX . "order_session SET
                order_id = '" . (int)$this->order_id . "',
                session_key = '" . $this->db->escape($this->session_key) . "',
                session_data = '" . $this->db->escape($json_data) . "',
                created_at = '" . $now . "',
                updated_at = '" . $now . "'
            ");
        }
        
        $this->is_dirty = false;
    }
    
    /**
     * Получава стойност от сесията
     * 
     * @param string $key Ключ (поддържа dot notation като 'shipping_method.code')
     * @param mixed $default Стойност по подразбиране
     * @return mixed
     */
    public function get($key, $default = null) {
        $this->loadSessionData();
        
        // Поддръжка на dot notation
        if (strpos($key, '.') !== false) {
            $keys = explode('.', $key);
            $value = $this->session_data;
            
            foreach ($keys as $k) {
                if (is_array($value) && isset($value[$k])) {
                    $value = $value[$k];
                } else {
                    return $default;
                }
            }
            
            return $value;
        }
        
        return isset($this->session_data[$key]) ? $this->session_data[$key] : $default;
    }
    
    /**
     * Задава стойност в сесията
     * 
     * @param string $key Ключ (поддържа dot notation)
     * @param mixed $value Стойност
     */
    public function set($key, $value) {
        $this->loadSessionData();
        
        // Поддръжка на dot notation
        if (strpos($key, '.') !== false) {
            $keys = explode('.', $key);
            $current = &$this->session_data;
            
            foreach ($keys as $k) {
                if (!isset($current[$k]) || !is_array($current[$k])) {
                    $current[$k] = [];
                }
                $current = &$current[$k];
            }
            
            $current = $value;
        } else {
            $this->session_data[$key] = $value;
        }
        
        $this->is_dirty = true;
        $this->saveSessionData('Set');
    }
    
    /**
     * Проверява дали ключ съществува в сесията
     * 
     * @param string $key Ключ (поддържа dot notation)
     * @return bool
     */
    public function has($key) {
        $this->loadSessionData();
        
        // Поддръжка на dot notation
        if (strpos($key, '.') !== false) {
            $keys = explode('.', $key);
            $current = $this->session_data;
            
            foreach ($keys as $k) {
                if (is_array($current) && isset($current[$k])) {
                    $current = $current[$k];
                } else {
                    return false;
                }
            }
            
            return true;
        }
        
        return isset($this->session_data[$key]);
    }
    
    /**
     * Премахва ключ от сесията
     * 
     * @param string $key Ключ (поддържа dot notation)
     */
    public function remove($key) {
        $this->loadSessionData();
        
        // Поддръжка на dot notation
        if (strpos($key, '.') !== false) {
            $keys = explode('.', $key);
            $last_key = array_pop($keys);
            $current = &$this->session_data;
            
            foreach ($keys as $k) {
                if (is_array($current) && isset($current[$k])) {
                    $current = &$current[$k];
                } else {
                    return; // Ключът не съществува
                }
            }
            
            if (is_array($current) && isset($current[$last_key])) {
                unset($current[$last_key]);
                $this->is_dirty = true;
                $this->saveSessionData('Remove');
            }
        } else {
            if (isset($this->session_data[$key])) {
                unset($this->session_data[$key]);
                $this->is_dirty = true;
                $this->saveSessionData('Remove');
            }
        }
    }
    
    /**
     * Изчиства всички данни от сесията
     */
    public function clear() {
        $this->session_data = [];
        $this->is_dirty = true;
        $this->saveSessionData('Clear');
    }
    
    /**
     * Получава session key-я
     * 
     * @return string
     */
    public function getSessionKey() {
        return $this->session_key;
    }
    
    /**
     * Задава order_id (полезно при работа с нови поръчки)
     * 
     * @param int $order_id
     */
    public function setOrderId($order_id) {
        $this->order_id = (int)$order_id;
        $this->is_loaded = false; // Принуждаваме повторно зареждане
    }
    
    /**
     * Получава всички данни от сесията
     * 
     * @return array
     */
    public function getAllData() {
        $this->loadSessionData();
        return $this->session_data;
    }
    
    /**
     * Почиства стари сесии
     * При първо зареждане на страницата - изчиства всички стари сесии за същия order_id
     * При общо почистване - изчиства всички стари сесии (по-стари от 12 часа)
     */
    public function cleanupOldSessions() {
        try {
            if ($this->order_id > 0) {
                // При редактиране на конкретна поръчка - изчистваме всички стари сесии за същия order_id
                // освен текущата сесия
                $this->db->query("
                    DELETE FROM " . DB_PREFIX . "order_session
                    WHERE order_id = '" . (int)$this->order_id . "'
                    AND session_key != '" . $this->db->escape($this->session_key) . "'
                ");

                $affected_rows_order = $this->db->query("SELECT ROW_COUNT() as count")->row['count'] ?? 0;
                // if ($affected_rows_order > 0) {
                //     F()->log->developer("OrderSession: Cleaned up {$affected_rows_order} old session records for order_id {$this->order_id}", __FILE__, __LINE__);
                // }
            }

            // Общо почистване на много стари сесии (по-стари от 12 часа)
            $cutoff_time = date('Y-m-d H:i:s', strtotime('-12 hours'));
            $this->db->query("
                DELETE FROM " . DB_PREFIX . "order_session
                WHERE updated_at < '" . $this->db->escape($cutoff_time) . "'
            ");

            $affected_rows_global = $this->db->query("SELECT ROW_COUNT() as count")->row['count'] ?? 0;
            // if ($affected_rows_global > 0) {
            //     F()->log->developer("OrderSession: Cleaned up {$affected_rows_global} old session records older than {$cutoff_time}", __FILE__, __LINE__);
            // }

        } catch (Exception $e) {
            // Тихо игнориране на грешки при почистване - не трябва да прекъсва основната функционалност
            // F()->log->developer("OrderSession cleanup error: " . $e->getMessage(), __FILE__, __LINE__);
        }
    }

    /**
     * Зарежда всички данни на поръчката в сесията
     * Използва се само при отваряне на поръчката за редактиране, не при AJAX заявки
     *
     * @param int $order_id ID на поръчката
     */
    public function loadOrderDataToSession($order_id) {
        if (!$order_id || $order_id <= 0) {
            return false;
        }

        try {
            // Зареждане на основни данни на поръчката
            $order_query = $this->db->query("
                SELECT * FROM " . DB_PREFIX . "order
                WHERE order_id = '" . (int)$order_id . "'
            ");

            if (!$order_query->num_rows) {
                return false;
            }

            $order_data = $order_query->row;
            $this->set('order_data', $order_data);

            // Зареждане на продуктите с техните опции
            $products_query = $this->db->query("
                SELECT * FROM " . DB_PREFIX . "order_product
                WHERE order_id = '" . (int)$order_id . "'
                ORDER BY order_product_id
            ");

            $products = [];
            foreach ($products_query->rows as $product) {
                // Зареждане на опциите за всеки продукт
                $options_query = $this->db->query("
                    SELECT * FROM " . DB_PREFIX . "order_option
                    WHERE order_id = '" . (int)$order_id . "'
                    AND order_product_id = '" . (int)$product['order_product_id'] . "'
                ");

                $product['options'] = $options_query->rows;

                // ВАЖНО: Запазваме реалния order_product_id като real_order_product_id
                $product['real_order_product_id'] = $product['order_product_id'];

                // Генерираме композитен ID и го записваме като order_product_id
                // Това гарантира съответствие с HTML формата и AJAX заявките
                $composite_id = $this->generateCompositeProductId($product['product_id'], $product['options']);
                $product['order_product_id'] = $composite_id;

                // DEBUG: Логиране на промяната на ID-тата
                error_log("OrderSession: Product ID mapping - real_id: {$product['real_order_product_id']}, composite_id: $composite_id, product_id: {$product['product_id']}");

                $products[] = $product;
            }

            $this->set('order_products', $products);

            // Зареждане на тоталите
            $totals_query = $this->db->query("
                SELECT * FROM " . DB_PREFIX . "order_total
                WHERE order_id = '" . (int)$order_id . "'
                ORDER BY sort_order
            ");

            $this->set('order_totals', $totals_query->rows);

            // Зареждане на история на поръчката
            $history_query = $this->db->query("
                SELECT * FROM " . DB_PREFIX . "order_history
                WHERE order_id = '" . (int)$order_id . "'
                ORDER BY date_added DESC
            ");

            $this->set('order_history', $history_query->rows);

            // Запазване на timestamp за зареждането
            $this->set('data_loaded_at', date('Y-m-d H:i:s'));
            $this->set('data_loaded_for_order_id', $order_id);

            return true;

        } catch (Exception $e) {
            error_log("OrderSession loadOrderDataToSession error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Проверява дали данните на поръчката са заредени в сесията
     *
     * @param int $order_id ID на поръчката за проверка
     * @return bool
     */
    public function isOrderDataLoaded($order_id = null) {
        $loaded_order_id = $this->get('data_loaded_for_order_id');

        if ($order_id) {
            return $loaded_order_id == $order_id;
        }

        return !empty($loaded_order_id);
    }

    /**
     * Получава данните на поръчката от сесията
     *
     * @param string $data_type Тип данни: 'order_data', 'order_products', 'order_totals', 'order_history'
     * @return mixed
     */
    public function getOrderData($data_type = 'order_data') {
        return $this->get($data_type, []);
    }

    /**
     * Актуализира продуктите в сесията (за AJAX операции)
     *
     * @param array $products Масив с продукти
     */
    public function updateOrderProducts($products) {
        $this->set('order_products', $products);
        $this->set('products_updated_at', date('Y-m-d H:i:s'));
    }

    /**
     * Добавя продукт в сесията
     *
     * @param array $product_data Данни за продукта
     */
    public function addOrderProduct($product_data) {
        $products = $this->get('order_products', []);

        // Генериране на композитен order_product_id на базата на product_id и опции
        $product_id = isset($product_data['product_id']) ? $product_data['product_id'] : 0;
        $options = isset($product_data['options']) ? $product_data['options'] : [];

        $composite_id = $this->generateCompositeProductId($product_id, $options);
        $product_data['order_product_id'] = $composite_id;
        $product_data['is_new'] = true; // Маркираме като нов продукт

        $products[] = $product_data;
        $this->updateOrderProducts($products);
    }

    /**
     * Премахва продукт от сесията
     *
     * @param string $order_product_id Композитен ID на продукта за премахване
     */
    public function removeOrderProduct($order_product_id) {
        $products = $this->get('order_products', []);

        foreach ($products as $key => $product) {
            if ($product['order_product_id'] == $order_product_id) {
                unset($products[$key]);
                break;
            }
        }

        $this->updateOrderProducts(array_values($products));
    }

    /**
     * Генерира композитен order_product_id на базата на product_id и опции
     *
     * @param int $product_id ID на продукта
     * @param array $options Масив с опции
     * @return string Композитен ID
     */
    public function generateCompositeProductId($product_id, $options = []) {
        $composite_id = (string)$product_id;

        if (!empty($options) && is_array($options)) {
            // Сортиране на опциите по product_option_id за консистентност
            usort($options, function($a, $b) {
                $a_option_id = isset($a['product_option_id']) ? (int)$a['product_option_id'] : 0;
                $b_option_id = isset($b['product_option_id']) ? (int)$b['product_option_id'] : 0;
                return $a_option_id - $b_option_id;
            });

            foreach ($options as $option) {
                $option_id = isset($option['product_option_id']) ? $option['product_option_id'] : '';
                $value_id = isset($option['product_option_value_id']) ? $option['product_option_value_id'] : '';

                if (!empty($option_id) && !empty($value_id)) {
                    $composite_id .= '_' . $option_id . '_' . $value_id;
                }
            }
        }

        return $composite_id;
    }

    /**
     * Проверява дали order_product_id е композитен (нов продукт) или реален (съществуващ)
     *
     * @param string $order_product_id ID за проверка
     * @return bool true ако е композитен, false ако е реален
     */
    public function isCompositeProductId($order_product_id) {
        // Композитните ID-та съдържат подчертавки или са по-дълги от обичайните числови ID-та
        return !is_numeric($order_product_id) || strpos($order_product_id, '_') !== false;
    }

    /**
     * Актуализира количеството на продукт в сесията
     *
     * @param string $order_product_id Композитен ID на продукта
     * @param int $quantity Ново количество
     */
    public function updateProductQuantity($order_product_id, $quantity) {
        $products = $this->get('order_products', []);

        foreach ($products as &$product) {
            if ($product['order_product_id'] == $order_product_id) {
                $product['quantity'] = (int)$quantity;
                $product['total'] = $product['price'] * $quantity;
                $this->is_dirty = true;
                break;
            }
        }

        $this->updateOrderProducts($products);
    }

    /**
     * Актуализира цената на продукт в сесията
     *
     * @param string $order_product_id Композитен ID на продукта
     * @param float $price Нова цена
     */
    public function updateProductPrice($order_product_id, $price) {
        $products = $this->get('order_products', []);

        foreach ($products as &$product) {
            if ($product['order_product_id'] == $order_product_id) {
                $product['price'] = (float)$price;
                $product['total'] = $product['quantity'] * $price;
                break;
            }
        }

        $this->updateOrderProducts($products);
    }

    public function updateOrderTotals($totals) {
        $this->set('order_totals', $totals);
    }

    public function getOrderTotals() {
        return $this->get('order_totals', []);
    }

    public function updateOrderTotal($code, $value) {
        $totals = $this->get('order_totals', []);
        $totals[$code] = $value;
        $this->updateOrderTotals($totals);
    }

    public function getOrderTotal($code) {
        $totals = $this->get('order_totals', []);
        return $totals[$code] ?? null;
    }

    /**
     * Деструктор - автоматично запазване при унищожаване на обекта
     */
    public function __destruct() {
        if ($this->is_dirty) {
            $this->saveSessionData('Destruct');
        }
    }
}
