<?php

namespace Theme25\Model\Extension;

class Total extends \Theme25\Model {

    protected $shippingHelper;

    public function __construct($registry) {
        parent::__construct($registry);
        $this->loadModelAs('sale/order', 'orders');
        $this->shippingHelper = \Theme25\Helper\ShippingHelper::getInstance($registry);
    }

    /**
     * Форматира сума като валута
     */
    private function formatCurrency($value, $currency_code = null, $currency_value = null) {
        if ($currency_code && $currency_value) {
            return $this->currency->format($value, $currency_code, $currency_value);
        } else {
            return $this->currency->format($value);
        }
    }

    public function getOrderTotals($order_id) {
        $order_totals = $this->orders->getOrderTotals($order_id);
        $order_totals = $this->reArrangeTotals($order_totals);
        $order_totals = $this->formatOrderTotals($order_totals);
        return $order_totals;
    }

    /**
     * Изчислява всички тотали на поръчката с оптимизирана логика
     *
     * ОПТИМИЗАЦИИ:
     * 1. Унифициране на източниците на данни (параметър/OrderSession/БД)
     * 2. Динамично преизчисляване на всички тотали на базата на актуалните продукти
     * 3. Валидация и преизчисляване на съществуващи тотали (ваучери, купони, такси)
     * 4. Правилна последователност: субтотал → доставка → данък → отстъпки → финален тотал
     * 5. Запазване на пълната информация за тоталите (кодове на купони, описания, метаданни)
     *
     * @param array|null $products_data Продукти за изчисление (приоритет 1)
     * @param int|null $order_id ID на поръчката
     * @param string $format Формат на връщане: 'detailed' или 'simple'
     * @return array Изчислени тотали
     */
    public function calculateTotals($products_data = null, $order_id = null, $format = 'detailed') {
        // СТЪПКА 1: Зареждане на съществуващи тотали от сесията (запазване на пълната информация)
        $totals = [];
        if ($order_id && $this->registry->has('order_session')) {
            if ($this->order_session->isOrderDataLoaded($order_id)) {
                $session_totals = $this->order_session->getOrderTotals();
                if (!empty($session_totals)) {
                    $totals = $session_totals;
                }
            }
        }

        // СТЪПКА 2: Получаване на актуалните продукти (унифициране на източниците на данни)
        $current_products = $this->getCurrentProducts($products_data, $order_id);

        // СТЪПКА 3: Изчисляване на субтотал от актуалните продукти
        $subtotal = $this->calculateSubTotal($current_products);

        // СТЪПКА 4: Инициализация на $total_sums с пълна информация от сесията
        $total_sums = []; // Запазваме цялата информация за всеки тотал
        foreach ($totals as $total) {
            $total_sums[$total['code']] = $total;
        }

        // СТЪПКА 5: Присвояване на subtotal и преизчисляване
        // Актуализираме subtotal в масива с пълната информация
        if (isset($total_sums['sub_total'])) {
            $total_sums['sub_total']['value'] = $subtotal;
        } else {
            $total_sums['sub_total'] = [
                'code' => 'sub_total',
                'title' => 'Цена на продуктите',
                'value' => $subtotal,
                'sort_order' => 1
            ];
        }

        // СТЪПКА 6: Обработка на съществуващи тотали (валидация и преизчисляване)
        if ($order_id) {
            $processed_totals = $this->processExistingTotals($order_id, $current_products, $subtotal);

            // Добавяне на валидираните/преизчислени тотали
            foreach ($processed_totals as $code => $value) {
                if (!in_array($code, ['sub_total', 'shipping', 'total'])) {
                    if (isset($total_sums[$code])) {
                        // Актуализираме стойността, запазвайки останалата информация
                        $total_sums[$code]['value'] = $value;
                    } else {
                        // Създаваме нов запис
                        $total_sums[$code] = [
                            'code' => $code,
                            'title' => ucfirst(str_replace('_', ' ', $code)),
                            'value' => $value,
                            'sort_order' => $this->getDefaultSortOrder($code)
                        ];
                    }
                }
            }
        }

        // СТЪПКА 7: Изчисляване на доставка на базата на актуалните продукти
        $shipping_data = $this->calculateShippingTotal($order_id, $current_products);
        if (isset($total_sums['shipping'])) {
            $total_sums['shipping']['value'] = $shipping_data['value'];
            $total_sums['shipping']['title'] = $shipping_data['title'];
        } else {
            $total_sums['shipping'] = [
                'code' => 'shipping',
                'title' => $shipping_data['title'],
                'value' => $shipping_data['value'],
                'sort_order' => 3
            ];
        }

        // СТЪПКА 8: Изчисляване на крайната сума (правилна последователност)
        $total = $subtotal;
        foreach ($total_sums as $code => $total_data) {
            if ($code !== 'sub_total' && $code !== 'total') {
                $value = is_array($total_data) ? $total_data['value'] : $total_data;
                $total += $value;
            }
        }
        $total = max(0, $total); // Не позволяваме отрицателни тотали

        if (isset($total_sums['total'])) {
            $total_sums['total']['value'] = $total;
        } else {
            $total_sums['total'] = [
                'code' => 'total',
                'title' => 'Общо',
                'value' => $total,
                'sort_order' => 99
            ];
        }

        // СТЪПКА 9: Подреждане на тоталите в правилния ред
        $total_sums = $this->reArrangeTotals($total_sums);

        // СТЪПКА 10: Добавяне на опция за опростен формат
        if ($format === 'simple') {
            // Връщаме само ключовете на тоталите и техните числови стойности
            $simple_totals = [];
            foreach ($total_sums as $code => $total_data) {
                if (is_array($total_data) && isset($total_data['value'])) {
                    $simple_totals[$code] = $total_data['value'];
                } else {
                    $simple_totals[$code] = $total_data;
                }
            }
            return $simple_totals;
        }

        // СТЪПКА 11: Ако не е поискан 'simple' формат, връщаме пълния масив с цялата информация
        return $this->formatOrderTotalsCurrency($total_sums);
    }

    /**
     * Преобразува простия формат в детайлен формат с code, title, value, text, sort_order
     */
    private function convertToDetailedFormat($total_sums, $shipping_data = null, $tax_data = null) {
        $detailed_totals = [];
        $sort_order = 1;

        // Дефиниране на заглавията за всеки код
        $default_titles = [
            'sub_total' => 'Цена на продуктите',
            'shipping' => $shipping_data['title'] ?? 'Доставка',
            'tax' => $tax_data['title'] ?? 'Данък',
            'voucher' => 'Ваучер',
            'coupon' => 'Купон',
            'total' => 'Общо'
        ];

        foreach ($total_sums as $code => $value) {
            $title = $default_titles[$code] ?? ucfirst(str_replace('_', ' ', $code));

            $detailed_totals[$code] = [
                'code' => $code,
                'title' => $title,
                'value' => (float)$value,
                'text' => $this->formatCurrency($value),
                'sort_order' => $sort_order++
            ];
        }

        return $detailed_totals;
    }

    /**
     * Връща стандартния sort_order за даден код на тотал
     *
     * @param string $code Код на тотала
     * @return int Sort order стойност
     */
    private function getDefaultSortOrder($code) {
        $default_sort_orders = [
            'sub_total' => 1,
            'shipping' => 3,
            'tax' => 5,
            'voucher' => 8,
            'coupon' => 9,
            'total' => 99
        ];

        return $default_sort_orders[$code] ?? 50; // Стандартна стойност за неизвестни кодове
    }

    /**
     * Получава актуалните продукти от различните източници (унифициране на данните)
     *
     * @param array|null $products_data Продукти подадени като параметър
     * @param int|null $order_id ID на поръчката
     * @return array Актуалните продукти за изчисления
     */
    private function getCurrentProducts($products_data = null, $order_id = null) {

        // Приоритет 1: Продукти подадени директно като параметър
        if (!empty($products_data) && is_array($products_data)) {
            return $products_data;
        }

        // Приоритет 2: Продукти от OrderSession (временни данни)
        if ($order_id && $this->registry->has('order_session')) {
            if ($this->order_session->isOrderDataLoaded($order_id)) {
                $session_products = $this->order_session->getOrderData('order_products');
                if (!empty($session_products)) {
                    return $session_products;
                }
            }
        }

        // Приоритет 3: Продукти от базата данни (fallback)
        if ($order_id) {
            $products = $this->getOrderProductsFromDatabase($order_id);
            return $products;
        }

        // Приоритет 4: Празен масив (нова поръчка без продукти)
        return [];
    }

    /**
     * Получава продуктите на поръчката от базата данни
     *
     * @param int $order_id ID на поръчката
     * @return array Продукти от базата данни
     */
    private function getOrderProductsFromDatabase($order_id) {
        $products = [];

        $query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "order_product
            WHERE order_id = '" . (int)$order_id . "'
            ORDER BY order_product_id
        ");

        foreach ($query->rows as $product) {
            // Зареждане на опциите за всеки продукт
            $options_query = $this->db->query("
                SELECT * FROM " . DB_PREFIX . "order_option
                WHERE order_id = '" . (int)$order_id . "'
                AND order_product_id = '" . (int)$product['order_product_id'] . "'
            ");

            $product['options'] = $options_query->rows;
            $products[] = $product;
        }

        return $products;
    }

    /**
     * Обработва съществуващите тотали (валидация и преизчисляване)
     *
     * @param int $order_id ID на поръчката
     * @param array $current_products Актуалните продукти
     * @param float $current_subtotal Актуалният субтотал
     * @return array Обработени тотали
     */
    private function processExistingTotals($order_id, $current_products, $current_subtotal) {
        $existing_totals = $this->getExistingTotals($order_id);
        $processed_totals = [];

        foreach ($existing_totals as $code => $value) {
            switch ($code) {
                case 'voucher':
                    // Валидиране и преизчисляване на ваучери спрямо актуалните продукти
                    $processed_totals[$code] = $this->validateVoucherTotal($value, $current_subtotal, $order_id);
                    break;

                case 'coupon':
                    // Преизчисляване на купони спрямо актуалните продукти
                    $processed_totals[$code] = $this->recalculateCouponTotal($order_id, $current_products, $current_subtotal);
                    break;

                case 'sub_total':
                case 'shipping':
                case 'total':
                    // Тези се изчисляват динамично, не ги запазваме от БД
                    break;

                default:
                    // Други тотали - запазваме стойността от БД
                    $processed_totals[$code] = $value;
                    break;
            }
        }

        return $processed_totals;
    }

    /**
     * Валидира и преизчислява ваучер тотал спрямо актуалните продукти
     */
    private function validateVoucherTotal($voucher_value, $current_subtotal, $order_id = null) {
        // СТЪПКА 1: Проверка дали има актуален субтотал
        if ($current_subtotal <= 0) {
            return 0; // Няма субтотал - няма отстъпка
        }

        // СТЪПКА 2: Ако имаме order_id, проверяваме актуалната информация за ваучера
        if ($order_id) {
            $voucher_record = $this->getVoucherRecordFromOrder($order_id);
            if ($voucher_record) {
                // Получаване на актуалната информация за ваучера
                $voucher_info = $this->getVoucherInfo($voucher_record['voucher_code']);
                if ($voucher_info) {
                    // Преизчисляване на ваучера спрямо актуалните условия
                    $voucher_value = $this->calculateVoucherDiscountForOrder($voucher_info, $current_subtotal);
                }
            }
        }

        // СТЪПКА 3: Валидация - ваучерът не може да бъде по-голям от субтотала
        $voucher_amount = abs($voucher_value);
        if ($voucher_amount > $current_subtotal) {
            return -$current_subtotal; // Максимална отстъпка = субтотала
        }

        // СТЪПКА 4: Гарантиране, че стойността е отрицателна (отстъпка)
        return -$voucher_amount;
    }

    /**
     * Преизчислява купон тотал спрямо актуалните продукти
     */
    private function recalculateCouponTotal($order_id, $current_products, $current_subtotal) {
        
        // СТЪПКА 1: Проверка дали има актуални продукти и субтотал
        if (empty($current_products) || $current_subtotal <= 0) {
            return 0; // Няма продукти или субтотал - няма отстъпка
        }

        // СТЪПКА 2: Получаване на информация за приложения купон от order_total таблицата
        $coupon_query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "order_total
            WHERE order_id = '" . (int)$order_id . "'
            AND code = 'coupon'
        ");

        if (!$coupon_query->num_rows) {
            return 0; // Няма приложен купон
        }

        $coupon_record = $coupon_query->row;

        // СТЪПКА 3: Извличане на купон кода от title-а (формат: "Купон (CODE)")
        $coupon_code = '';
        if (preg_match('/\(([^)]+)\)/', $coupon_record['title'], $matches)) {
            $coupon_code = $matches[1];
        }

        if (empty($coupon_code)) {
            // Ако не можем да извлечем кода, премахваме купона (връщаме 0)
            return 0;
        }

        // СТЪПКА 4: Получаване на актуалната информация за купона от coupon таблицата
        $coupon_info_query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "coupon
            WHERE code = '" . $this->db->escape($coupon_code) . "'
            AND status = '1'
        ");

        if (!$coupon_info_query->num_rows) {
            // Ако купонът не съществува или е неактивен, премахваме го
            return 0;
        }

        $coupon_info = $coupon_info_query->row;

        // СТЪПКА 5: Проверка на условията на купона (минимална сума, валидност, и т.н.)
        $discount_amount = $this->calculateCouponDiscountForProducts($coupon_info, $current_products, $current_subtotal);

        // Временен debug лог
        file_put_contents(DIR_LOGS . 'debug_coupon.txt',
            "recalculateCouponTotal: coupon_code={$coupon_code}, discount_amount={$discount_amount}, final_result=" . (-abs($discount_amount)) . "\n",
            FILE_APPEND
        );

        // СТЪПКА 6: Гарантиране, че отстъпката е отрицателна стойност
        return -abs($discount_amount);
    }

    /**
     * Получава запис за ваучер от order_total таблицата
     */
    private function getVoucherRecordFromOrder($order_id) {
        $voucher_query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "order_total
            WHERE order_id = '" . (int)$order_id . "'
            AND code = 'voucher'
        ");

        if (!$voucher_query->num_rows) {
            return null;
        }

        $voucher_record = $voucher_query->row;

        // Извличане на ваучер кода от title-а (формат: "Ваучер (CODE)")
        $voucher_code = '';
        if (preg_match('/\(([^)]+)\)/', $voucher_record['title'], $matches)) {
            $voucher_code = $matches[1];
        }

        return [
            'voucher_code' => $voucher_code,
            'current_value' => (float)$voucher_record['value']
        ];
    }

    /**
     * Получава информация за ваучер от voucher таблицата
     */
    private function getVoucherInfo($voucher_code) {
        if (empty($voucher_code)) {
            return null;
        }

        $voucher_query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "voucher
            WHERE code = '" . $this->db->escape($voucher_code) . "'
            AND status = '1'
        ");

        return $voucher_query->num_rows ? $voucher_query->row : null;
    }

    /**
     * Изчислява отстъпката от ваучер за поръчка
     */
    private function calculateVoucherDiscountForOrder($voucher_info, $current_subtotal) {
        // СТЪПКА 1: Проверка на валидността на ваучера
        $now = date('Y-m-d');
        if (isset($voucher_info['date_start']) && $voucher_info['date_start'] > $now) {
            return 0; // Ваучерът още не е валиден
        }
        if (isset($voucher_info['date_end']) && $voucher_info['date_end'] < $now) {
            return 0; // Ваучерът е изтекъл
        }

        // СТЪПКА 2: Проверка на минималната сума (ако има такава)
        if (isset($voucher_info['total']) && $voucher_info['total'] > 0 && $current_subtotal < (float)$voucher_info['total']) {
            return 0; // Субтоталът е под минималната сума за ваучера
        }

        // СТЪПКА 3: Изчисляване на отстъпката
        $voucher_amount = (float)$voucher_info['amount'];

        // Ваучерът не може да бъде по-голям от субтотала
        return min($voucher_amount, $current_subtotal);
    }

    /**
     * Изчислява отстъпката от купон за конкретни продукти
     */
    private function calculateCouponDiscountForProducts($coupon_info, $current_products, $current_subtotal) {
        // СТЪПКА 1: Проверка на минималната сума за купона
        if (isset($coupon_info['total']) && $coupon_info['total'] > 0 && $current_subtotal < (float)$coupon_info['total']) {
            return 0; // Субтоталът е под минималната сума за купона
        }

        // СТЪПКА 2: Проверка на валидността на купона
        $now = date('Y-m-d');
        if (isset($coupon_info['date_start']) && $coupon_info['date_start'] > $now) {
            return 0; // Купонът още не е валиден
        }
        if (isset($coupon_info['date_end']) && $coupon_info['date_end'] < $now) {
            return 0; // Купонът е изтекъл
        }

        // СТЪПКА 3: Определяне на приложимите продукти
        $applicable_subtotal = $current_subtotal;

        // Ако купонът е ограничен до конкретни продукти
        if (!empty($coupon_info['product'])) {
            $coupon_products = explode(',', $coupon_info['product']);
            $applicable_subtotal = 0;

            foreach ($current_products as $product) {
                if (in_array($product['product_id'], $coupon_products)) {
                    $applicable_subtotal += (float)$product['price'] * (float)$product['quantity'];
                }
            }

            if ($applicable_subtotal <= 0) {
                return 0; // Няма приложими продукти
            }
        }

        // СТЪПКА 4: Изчисляване на отстъпката според типа
        $discount_amount = 0;

        if ($coupon_info['type'] == 'F') {
            // Фиксирана сума - не може да бъде повече от приложимия субтотал
            $discount_amount = min((float)$coupon_info['discount'], $applicable_subtotal);
        } else {
            // Процентна отстъпка (P)
            $discount_amount = ($applicable_subtotal * (float)$coupon_info['discount']) / 100;
            // Ограничаване до максимум приложимия субтотал
            $discount_amount = min($discount_amount, $applicable_subtotal);
        }

        // Временен debug лог
        file_put_contents('debug_coupon.txt',
            "calculateCouponDiscountForProducts: type={$coupon_info['type']}, discount={$coupon_info['discount']}, applicable_subtotal={$applicable_subtotal}, discount_amount={$discount_amount}\n",
            FILE_APPEND
        );

        // СТЪПКА 5: Гарантиране, че отстъпката не е отрицателна
        return max(0, $discount_amount);
    }

    /**
     * Преизчислява такси спрямо актуалния субтотал
     */
    private function recalculateFeeTotal($fee_code, $original_value, $current_subtotal) {
        // За повечето такси запазваме оригиналната стойност
        // Може да се добави специфична логика за различни типове такси

        switch ($fee_code) {
            case 'low_order_fee':
                // Такса за малка поръчка - проверяваме дали все още е приложима
                $min_order_amount = $this->config->get('config_min_order_amount') ?: 50;
                if ($current_subtotal >= $min_order_amount) {
                    return 0; // Премахваме таксата
                }
                break;
        }

        return $original_value;
    }

    public function calculateSubTotal($products_data = null) {
        if(!$products_data) {
            $products_data = $this->cart->getProducts();
        }

        $subtotal = 0;

        if (!empty($products_data)) {
            foreach ($products_data as $product) {
                $quantity = isset($product['quantity']) ? (float)$product['quantity'] : 0;
                $price = isset($product['price']) ? (float)$product['price'] : 0;
                $subtotal += $quantity * $price;
            }
        } else {
            // Ако няма подадени продукти, приемаме междинна сума 0.00
            $subtotal = 0;
        }

        return $subtotal;
    }

    public function calculateShippingTotal($order_id = null, $products_data = null) {
        // Ако имаме order_id, получаваме информацията за поръчката
        if ($order_id) {
            $order_info = $this->orders->getOrder($order_id);
            $shipping_code = $order_info['shipping_code'] ?? '';
            $shipping_method = $order_info['shipping_method'] ?? '';
        } else {
            // За нови поръчки използваме данни от OrderSession или конфигурацията
            $shipping_code = $this->order_session->get('shipping_method.code', '');
            $shipping_method = $this->order_session->get('shipping_method.title', '');
        }

        // Ако няма подадени продукти, използваме актуалните продукти
        if (empty($products_data)) {
            $products_data = $this->getCurrentProducts(null, $order_id);
        }

        $shipping_value = 0;
        $shipping_title = 'Доставка';

        $active_shipping_methods = $this->shippingHelper->getActiveShippingMethods();

        // Проверка за безплатна доставка
        if (strpos($shipping_code, 'free') !== false || empty($shipping_code)) {
            $shipping_value = 0;
            $shipping_title = 'Безплатна доставка';
        } else {
            // Опит за зареждане на специфичния модул за доставка
            try {
                // Извличане на основния код на модула (преди точката)
                $module_code = explode('.', $shipping_code)[0];

                // Опит за зареждане на модела за доставка
                $model_class = '\\Theme25\\Model\\Extension\\Shipping\\'.ucfirst($module_code);
                $model = new $model_class($this->registry);

                if ($model) {
                    // Проверка дали модела има метод за изчисляване на цената
                    if (is_callable([$model, 'getQuote'])) {
                        // Подготовка на данни за изчисляване с актуалните продукти
                        $address = $this->prepareShippingAddress($order_id);

                        // Подаване на актуалните продукти към модула за доставка
                        $quote_data = $model->getQuote($address, $products_data);

                        if (isset($quote_data['quote'][$shipping_code])) {
                            $shipping_value = $quote_data['quote'][$shipping_code]['cost'];
                            $shipping_title = $quote_data['quote'][$shipping_code]['title'];
                        }
                    }
                }
            } catch (Exception $e) {
                // При грешка използваме фиксирана стойност или стойност от базата данни
                if ($order_id) {
                    $existing_shipping = $this->db->query("SELECT value, title FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'shipping'");
                    if ($existing_shipping->num_rows) {
                        $shipping_value = (float)$existing_shipping->row['value'];
                        $shipping_title = $existing_shipping->row['title'];
                    }
                } else {
                    // Fallback към конфигурационна стойност
                    $shipping_value = (float)$this->config->get('config_shipping_default_cost') ?: 0;
                }
            }
        }

        return [
            'value' => $shipping_value,
            'title' => $shipping_title,
            'code' => 'shipping'
        ];
    }


    /**
     * Изчислява данъка за поръчката
     */
    public function calculateTaxTotal($products_data = null, $order_id = null) {
        $tax_value = 0;
        $tax_title = 'Данък';

        // Ако имаме order_id, получаваме съществуващия данък
        if ($order_id) {
            $existing_tax = $this->db->query("SELECT value, title FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'tax'");
            if ($existing_tax->num_rows) {
                $tax_value = (float)$existing_tax->row['value'];
                $tax_title = $existing_tax->row['title'];
            }
        } else {
            // За нови поръчки изчисляваме данъка от продуктите
            if ($products_data && $this->config->get('config_tax')) {
                foreach ($products_data as $product) {
                    if (isset($product['tax_class_id']) && $product['tax_class_id']) {
                        $quantity = isset($product['quantity']) ? (float)$product['quantity'] : 0;
                        $price = isset($product['price']) ? (float)$product['price'] : 0;

                        // Изчисляване на данъка за продукта
                        $product_tax = $this->tax->calculate($price, $product['tax_class_id'], $this->config->get('config_tax'));
                        $tax_value += ($product_tax - $price) * $quantity;
                    }
                }
            }
        }

        return [
            'value' => $tax_value,
            'title' => $tax_title,
            'code' => 'tax'
        ];
    }

    /**
     * Подготвя адреса за доставка за изчисляване на цената
     */
    private function prepareShippingAddress($order_id = null) {
        if ($order_id) {
            $order_info = $this->orders->getOrder($order_id);
            return [
                'country_id' => $order_info['shipping_country_id'],
                'zone_id' => $order_info['shipping_zone_id'],
                'postcode' => $order_info['shipping_postcode'],
                'city' => $order_info['shipping_city'],
                'address_1' => $order_info['shipping_address_1'],
                'address_2' => $order_info['shipping_address_2']
            ];
        } else {
            // За нови поръчки използваме данни от OrderSession
            return [
                'country_id' => $this->order_session->get('shipping_address.country_id', $this->config->get('config_country_id')),
                'zone_id' => $this->order_session->get('shipping_address.zone_id', $this->config->get('config_zone_id')),
                'postcode' => $this->order_session->get('shipping_address.postcode', ''),
                'city' => $this->order_session->get('shipping_address.city', ''),
                'address_1' => $this->order_session->get('shipping_address.address_1', ''),
                'address_2' => $this->order_session->get('shipping_address.address_2', '')
            ];
        }
    }

    /**
     * Получава съществуващите тотали от базата данни
     */
    private function getExistingTotals($order_id) {
        $existing_totals = [];

        // Директна БД заявка за да избегнем кеширане
        $totals_query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "order_total
            WHERE order_id = '" . (int)$order_id . "'
            ORDER BY sort_order
        ");

        foreach ($totals_query->rows as $total) {
            $existing_totals[$total['code']] = (float)$total['value'];
        }

        return $existing_totals;
    }

    /**
     * Прилага ваучер към поръчката
     */
    public function applyVoucherToOrder($order_id, $voucher_code, $voucher_info) {
        // СТЪПКА 1: Гарантиране, че има sub_total запис в order_total таблицата
        $this->ensureSubTotalExists($order_id);

        // СТЪПКА 2: Премахване на съществуващи ваучери
        $this->db->query("DELETE FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'voucher'");

        // СТЪПКА 3: Валидация на ваучера
        $voucher_amount_raw = (float)$voucher_info['amount'];
        if ($voucher_amount_raw <= 0) {
            throw new Exception('Ваучерът не може да бъде приложен - невалидна стойност');
        }

        // СТЪПКА 4: Гарантиране, че стойността е отрицателна (отстъпка)
        $voucher_amount = abs($voucher_amount_raw) * -1;

        // СТЪПКА 5: Добавяне на новия ваучер
        $this->db->query("INSERT INTO " . DB_PREFIX . "order_total SET
            order_id = '" . (int)$order_id . "',
            code = 'voucher',
            title = 'Ваучер (" . $this->db->escape($voucher_code) . ")',
            value = '" . (float)$voucher_amount . "',
            sort_order = '8'");
    }

    /**
     * Прилага купон към поръчката
     */
    public function applyCouponToOrder($order_id, $coupon_code, $coupon_info) {
        // СТЪПКА 1: Гарантиране, че има sub_total запис в order_total таблицата
        $this->ensureSubTotalExists($order_id);

        // СТЪПКА 2: Премахване на съществуващи купони
        $this->db->query("DELETE FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'coupon'");

        // СТЪПКА 3: Изчисляване на отстъпката
        $discount_amount = $this->calculateCouponDiscount($order_id, $coupon_info);

        // СТЪПКА 4: Проверка дали отстъпката е валидна
        if ($discount_amount <= 0) {
            throw new Exception('Купонът не може да бъде приложен - невалидна отстъпка');
        }

        // СТЪПКА 5: Гарантиране, че стойността е отрицателна (отстъпка)
        $coupon_amount = abs((float)$discount_amount) * -1;

        // СТЪПКА 6: Добавяне на новия купон
        $sql = "INSERT INTO " . DB_PREFIX . "order_total SET
            order_id = '" . (int)$order_id . "',
            code = 'coupon',
            title = 'Купон (" . $this->db->escape($coupon_code) . ")',
            value = '" . (float)$coupon_amount . "',
            sort_order = '9'";

        // Временен debug лог
        file_put_contents('debug_coupon.txt',
            "applyCouponToOrder: SQL={$sql}, discount_amount={$discount_amount}, coupon_amount={$coupon_amount}\n",
            FILE_APPEND
        );

        $this->db->query($sql);
    }

    /**
     * Прилага купон към поръчката с актуални продуктни данни
     * Използва се когато имаме актуални данни от формата вместо от базата данни
     */
    public function applyCouponToOrderWithProducts($order_id, $coupon_code, $coupon_info, $products_data) {

        // СТЪПКА 1: Гарантиране, че има sub_total запис с актуални данни
        $this->ensureSubTotalExistsWithProducts($order_id, $products_data);

        // СТЪПКА 2: Премахване на съществуващи купони
        $this->db->query("DELETE FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'coupon'");

        // СТЪПКА 3: Изчисляване на отстъпката с актуални данни
        $discount_amount = $this->calculateCouponDiscountWithProducts($coupon_info, $products_data);

        // СТЪПКА 4: Проверка дали отстъпката е валидна
        if ($discount_amount <= 0) {
            throw new Exception('Купонът не може да бъде приложен - невалидна отстъпка');
        }

        // СТЪПКА 5: Гарантиране, че стойността е отрицателна (отстъпка)
        $coupon_amount = abs((float)$discount_amount) * -1;

        // СТЪПКА 6: Добавяне на новия купон
        $sql = "INSERT INTO " . DB_PREFIX . "order_total SET
            order_id = '" . (int)$order_id . "',
            code = 'coupon',
            title = 'Купон (" . $this->db->escape($coupon_code) . ")',
            value = '" . (float)$coupon_amount . "',
            sort_order = '9'";

        $this->db->query($sql);
    }

    /**
     * Гарантира, че съществува sub_total запис в order_total таблицата
     */
    private function ensureSubTotalExists($order_id) {
        // Проверка дали съществува sub_total запис
        $subtotal_exists = $this->db->query("
            SELECT COUNT(*) as count
            FROM " . DB_PREFIX . "order_total
            WHERE order_id = '" . (int)$order_id . "' AND code = 'sub_total'
        ");

        if ($subtotal_exists->row['count'] == 0) {
            // Изчисляване на субтотал от продуктите
            $products_query = $this->db->query("
                SELECT * FROM " . DB_PREFIX . "order_product
                WHERE order_id = '" . (int)$order_id . "'
            ");

            $subtotal = 0;
            if ($products_query->num_rows) {
                foreach ($products_query->rows as $product) {
                    $subtotal += (float)$product['price'] * (float)$product['quantity'];
                }
            }

            // Създаване на sub_total запис
            $this->db->query("INSERT INTO " . DB_PREFIX . "order_total SET
                order_id = '" . (int)$order_id . "',
                code = 'sub_total',
                title = 'Цена на продуктите',
                value = '" . (float)$subtotal . "',
                sort_order = '1'");
        }
    }

    /**
     * Гарантира, че съществува sub_total запис с актуални продуктни данни
     */
    private function ensureSubTotalExistsWithProducts($order_id, $products_data) {
        // Изчисляване на субтотал от актуалните продуктни данни
        $subtotal = 0;
        if (!empty($products_data)) {
            foreach ($products_data as $product) {
                $quantity = isset($product['quantity']) ? (float)$product['quantity'] : 0;
                $price = isset($product['price']) ? (float)$product['price'] : 0;
                $subtotal += $quantity * $price;
            }
        }

        // Премахване на съществуващ sub_total запис
        $this->db->query("DELETE FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'sub_total'");

        // Създаване на нов sub_total запис с актуални данни
        $this->db->query("INSERT INTO " . DB_PREFIX . "order_total SET
            order_id = '" . (int)$order_id . "',
            code = 'sub_total',
            title = 'Цена на продуктите',
            value = '" . (float)$subtotal . "',
            sort_order = '1'");
    }

    /**
     * Изчислява отстъпката от купон
     */
    public function calculateCouponDiscount($order_id, $coupon_info) {
        // Получаване на актуалните продукти за изчисляване на субтотал
        $products_query = $this->db->query("
            SELECT * FROM " . DB_PREFIX . "order_product
            WHERE order_id = '" . (int)$order_id . "'
        ");

        // Изчисляване на актуалния субтотал от продуктите
        $subtotal = 0;
        if ($products_query->num_rows) {
            foreach ($products_query->rows as $product) {
                $subtotal += (float)$product['price'] * (float)$product['quantity'];
            }
        }

        // Fallback - опит за получаване от order_total таблицата
        if ($subtotal <= 0) {
            $subtotal_query = $this->db->query("SELECT value FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'sub_total'");
            $subtotal = $subtotal_query->num_rows ? (float)$subtotal_query->row['value'] : 0;
        }

        if ($subtotal <= 0) {
            return 0;
        }

        $discount_amount = 0;

        if ($coupon_info['type'] == 'F') {
            // Фиксирана сума - не може да бъде повече от междинната сума
            $discount_amount = min((float)$coupon_info['discount'], $subtotal);
        } else {
            // Процентна отстъпка (P)
            $discount_amount = ($subtotal * (float)$coupon_info['discount']) / 100;
            // Ограничаване до максимум междинната сума
            $discount_amount = min($discount_amount, $subtotal);
        }

        // Гарантиране, че отстъпката не е отрицателна
        return max(0, $discount_amount);
    }

    /**
     * Изчислява отстъпката от купон с актуални продуктни данни
     */
    public function calculateCouponDiscountWithProducts($coupon_info, $products_data) {
        // Изчисляване на актуалния субтотал от продуктните данни
        $subtotal = 0;
        if (!empty($products_data)) {
            foreach ($products_data as $product) {
                $quantity = isset($product['quantity']) ? (float)$product['quantity'] : 0;
                $price = isset($product['price']) ? (float)$product['price'] : 0;
                $subtotal += $quantity * $price;
            }
        }

        if ($subtotal <= 0) {
            return 0;
        }

        $discount_amount = 0;

        if ($coupon_info['type'] == 'F') {
            // Фиксирана сума - не може да бъде повече от междинната сума
            $discount_amount = min((float)$coupon_info['discount'], $subtotal);
        } else {
            // Процентна отстъпка (P)
            $discount_amount = ($subtotal * (float)$coupon_info['discount']) / 100;
            // Ограничаване до максимум междинната сума
            $discount_amount = min($discount_amount, $subtotal);
        }

        // Гарантиране, че отстъпката не е отрицателна
        $final_discount = max(0, $discount_amount);

        return $final_discount;
    }

    /**
     * Преизчислява общите суми на поръчката
     */
    public function recalculateOrderTotals($order_id) {
        // Получаване на всички суми
        $totals_query = $this->db->query("SELECT * FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' ORDER BY sort_order");

        $subtotal = 0;
        $shipping = 0;
        $voucher = 0;
        $coupon = 0;
        $tax = 0;
        $other_totals = 0;

        foreach ($totals_query->rows as $total) {
            switch ($total['code']) {
                case 'sub_total':
                    $subtotal = (float)$total['value'];
                    break;
                case 'shipping':
                    $shipping = (float)$total['value'];
                    break;
                case 'voucher':
                    $voucher = (float)$total['value']; // Вече отрицателна стойност
                    break;
                case 'coupon':
                    $coupon = (float)$total['value']; // Вече отрицателна стойност
                    break;
                // case 'tax':
                //     $tax = (float)$total['value'];
                //     break;
                case 'total':
                    // Пропускаме total - ще го изчислим наново
                    break;
                default:
                    // Други суми (например handling, low_order_fee, etc.)
                    $other_totals += (float)$total['value'];
                    break;
            }
        }

        // Изчисляване на новата обща сума
        $new_total = $subtotal + $shipping + $tax + $other_totals + $voucher + $coupon;
        $new_total = max(0, $new_total);

        // Актуализиране на общата сума в базата данни
        $this->db->query("UPDATE " . DB_PREFIX . "order_total SET
            value = '" . (float)$new_total . "',
            title = 'Общо'
            WHERE order_id = '" . (int)$order_id . "' AND code = 'total'");

        // Ако няма запис за total, създаваме го
        $total_exists = $this->db->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "order_total WHERE order_id = '" . (int)$order_id . "' AND code = 'total'");
        if ($total_exists->row['count'] == 0) {
            $this->db->query("INSERT INTO " . DB_PREFIX . "order_total SET
                order_id = '" . (int)$order_id . "',
                code = 'total',
                title = 'Общо',
                value = '" . (float)$new_total . "',
                sort_order = '99'");
        }

        // Актуализиране на общата сума в основната order таблица
        $this->db->query("UPDATE " . DB_PREFIX . "order SET total = '" . (float)$new_total . "' WHERE order_id = '" . (int)$order_id . "'");

        // Връщане на актуализираните суми
        return $this->getOrderTotals($order_id);
    }
	
    
    /**
     * Изчислява общите суми на базата на продуктите
     */
    public function calculateOrderTotalsFromProducts($order_id, $products_data, $return_like_db_records = false) {

        // Използваме новия calculateTotals метод с детайлен формат
        $detailed_totals = $this->calculateTotals($products_data, $order_id, 'detailed');

        // Ако няма продукти, нулираме определени тотали
        $noProducts = empty($products_data);
        if ($noProducts) {
            // Нулираме всички тотали освен sub_total и total
            foreach ($detailed_totals as $code => $total_data) {
                if (!in_array($code, ['sub_total', 'total'])) {
                    $detailed_totals[$code]['value'] = 0;
                    $detailed_totals[$code]['text'] = $this->formatCurrency(0);
                }
            }
            // Преизчисляваме total
            $new_total = max(0, $detailed_totals['sub_total']['value']);
            $detailed_totals['total']['value'] = $new_total;
            $detailed_totals['total']['text'] = $this->formatCurrency($new_total);
        }

        // Ако е поискано връщане като записи от БД
        if ($return_like_db_records) {
            $db_like_records = [];

            foreach ($detailed_totals as $code => $total_data) {
                $db_like_records[] = [
                    'order_id' => $order_id,
                    'code' => $total_data['code'],
                    'title' => $total_data['title'],
                    'value' => $total_data['value'],
                    'sort_order' => $total_data['sort_order']
                ];
            }
            return $this->formatOrderTotals($db_like_records);
        }

        return $detailed_totals;
    }

   
    public function reArrangeTotals($total_sums) {
        $new_total_sums = [];
        $main_totals = ['sub_total', 'shipping', 'tax', 'voucher', 'coupon'];

        // Първо добавяме основните тотали в правилния ред
        foreach ($main_totals as $main_code) {
            if (isset($total_sums[$main_code])) {
                $new_total_sums[$main_code] = $total_sums[$main_code];
            }
        }

        // След това добавяме всички останали тотали (освен 'total')
        foreach ($total_sums as $code => $value) {
            if (!in_array($code, $main_totals) && $code !== 'total') {
                $new_total_sums[$code] = $value;
            }
        }

        // Накрая добавяме 'total' тотала
        if (isset($total_sums['total'])) {
            $new_total_sums['total'] = $total_sums['total'];
        }

        return $new_total_sums;
    }

    /**
     * Форматира общите суми с правилната валута
     */
    public function formatOrderTotals($totals) {
        $formatted_totals = [];
        foreach ($totals as $code => $total) {
            $formatted_totals[$code] = [
                'code' => $code,
                'title' => $total['title'],
                'text' => $this->formatCurrency($total['value']),
                'value' => $total['value'],
                'sort_order' => $total['sort_order']
            ];
        }
        return $formatted_totals;
    }

    public function formatOrderTotalsCurrency($totals) {
        foreach ($totals as $code => &$total) {
            $total['text'] = $this->formatCurrency($total['value']);
        }
        return $totals;
    }


}