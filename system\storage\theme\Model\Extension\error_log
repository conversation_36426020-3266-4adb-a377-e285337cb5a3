[24-Aug-2025 12:10:26 Europe/Sofia] PHP Fatal error:  Uncaught Exception: Model 'deliveryModel' not found in /home/<USER>/storage_theme25/theme/CommonMethods.php:1269
Stack trace:
#0 /home/<USER>/storage_theme25/theme/Helper/ShippingHelper.php(40): Theme25\CommonMethods->getModel('deliveryModel')
#1 /home/<USER>/storage_theme25/theme/Model/Extension/Total.php(593): Theme25\Helper\ShippingHelper->getActiveShippingMethods()
#2 /home/<USER>/storage_theme25/theme/Model/Extension/Total.php(104): Theme25\Model\Extension\Total->calculateShippingTotal(50199, Array)
#3 [internal function]: Theme25\Model\Extension\Total->calculateTotals(Array, 50199, true)
#4 /home/<USER>/storage_theme25/theme/ModelProcessor.php(451): call_user_func_array(Array, Array)
#5 /home/<USER>/storage_theme25/theme/ModelProcessor.php(319): Theme25\ModelProcessor->callModelMethod(Object(Registry), Array, 'extension/total...', Array)
#6 /home/<USER>/theme25/system/engine/proxy.php(25): Theme25\ModelProcessor->Theme25\{closure}(Array, Array)
#7 /home/<USER>/storage_theme25/theme/Backend/Controller/Sale/Order in /home/<USER>/storage_theme25/theme/CommonMethods.php on line 1269
[24-Aug-2025 12:12:14 Europe/Sofia] OrderSession: Product ID mapping - real_id: 97429, composite_id: 5002518_2112_6823, product_id: 5002518
[24-Aug-2025 12:12:14 Europe/Sofia] OrderSession: Product ID mapping - real_id: 97430, composite_id: 5010307_4962_17127, product_id: 5010307
[24-Aug-2025 12:12:14 Europe/Sofia] OrderSession: Product ID mapping - real_id: 97431, composite_id: 5010300_4955_17113, product_id: 5010300
[24-Aug-2025 12:12:14 Europe/Sofia] OrderSession: Product ID mapping - real_id: 97432, composite_id: 5010269_4943_17043, product_id: 5010269
[24-Aug-2025 12:12:40 Europe/Sofia] PHP Fatal error:  Uncaught Exception: Error: Table 'rakla_test.oc_delivery_methods' doesn't exist<br />Error No: 1146<br />SELECT * FROM `oc_delivery_methods` ORDER BY `sort_order` ASC, `name` ASC in /home/<USER>/theme25/system/library/db/mysqli.php:40
Stack trace:
#0 /home/<USER>/theme25/system/library/db.php(16): DB\MySQLi->query('SELECT * FROM `...', Array)
#1 /home/<USER>/storage_theme25/theme/SecondDB.php(64): DB->query('SELECT * FROM `...', Array)
#2 /home/<USER>/storage_theme25/theme/Helper/DatabaseHelperTrait.php(76): Theme25\SecondDB->query('SELECT * FROM `...')
#3 /home/<USER>/storage_theme25/theme/Model/Setting/Delivery.php(131): Theme25\Model->dbQuery('SELECT * FROM `...')
#4 [internal function]: Theme25\Model\Setting\Delivery->getDeliveryMethods()
#5 /home/<USER>/storage_theme25/theme/ModelProcessor.php(451): call_user_func_array(Array, Array)
#6 /home/<USER>/storage_theme25/theme/ModelProcessor.php(319): Theme25\ModelProcessor->callModelMethod(Object(Registry), Array, 'setting/deliver...', Array)
#7 /home/<USER>/theme25/ in /home/<USER>/theme25/system/library/db/mysqli.php on line 40
[24-Aug-2025 12:18:10 Europe/Sofia] OrderSession: Product ID mapping - real_id: 97429, composite_id: 5002518_2112_6823, product_id: 5002518
[24-Aug-2025 12:18:10 Europe/Sofia] OrderSession: Product ID mapping - real_id: 97430, composite_id: 5010307_4962_17127, product_id: 5010307
[24-Aug-2025 12:18:10 Europe/Sofia] OrderSession: Product ID mapping - real_id: 97431, composite_id: 5010300_4955_17113, product_id: 5010300
[24-Aug-2025 12:18:10 Europe/Sofia] OrderSession: Product ID mapping - real_id: 97432, composite_id: 5010269_4943_17043, product_id: 5010269
[24-Aug-2025 12:18:17 Europe/Sofia] PHP Fatal error:  Uncaught Exception: Error: Table 'rakla_test.oc_delivery_methods' doesn't exist<br />Error No: 1146<br />SELECT * FROM `oc_delivery_methods` ORDER BY `sort_order` ASC, `name` ASC in /home/<USER>/theme25/system/library/db/mysqli.php:40
Stack trace:
#0 /home/<USER>/theme25/system/library/db.php(16): DB\MySQLi->query('SELECT * FROM `...', Array)
#1 /home/<USER>/storage_theme25/theme/SecondDB.php(64): DB->query('SELECT * FROM `...', Array)
#2 /home/<USER>/storage_theme25/theme/Helper/DatabaseHelperTrait.php(76): Theme25\SecondDB->query('SELECT * FROM `...')
#3 /home/<USER>/storage_theme25/theme/Model/Setting/Delivery.php(150): Theme25\Model->dbQuery('SELECT * FROM `...')
#4 [internal function]: Theme25\Model\Setting\Delivery->getDeliveryMethods()
#5 /home/<USER>/storage_theme25/theme/ModelProcessor.php(451): call_user_func_array(Array, Array)
#6 /home/<USER>/storage_theme25/theme/ModelProcessor.php(319): Theme25\ModelProcessor->callModelMethod(Object(Registry), Array, 'setting/deliver...', Array)
#7 /home/<USER>/theme25/ in /home/<USER>/theme25/system/library/db/mysqli.php on line 40
