<?php

namespace Theme25\Delivery;

/**
 * Конфигурация за фиксирана цена
 */
class Flat
{

    protected $registry;

    /**
     * Код на услугата
     */
    const CODE = 'flat';

    /**
     * Име на услугата
     */
    const NAME = 'Фиксирана цена за доставка';

    /**
     * Получава настройките за конфигуриране на услугата
     *
     * @return array
     */
    public static function getConfigFields()
    {
        return [
            // TAB: Основни настройки
            'main_settings_group' => [
                'type' => 'group',
                'label' => 'Основни настройки',
                'subfields' => [
                    'price' => [
                        'type' => 'text',
                        'label' => 'Фиксирана цена за доставка',
                        'required' => true,
                        'connection_required' => true
                    ],
                    'free_shipping_total' => [
                        'type' => 'text',
                        'label' => 'Безплатна доставка над стойност',
                        'required' => true,
                        'connection_required' => true
                    ]
                ],
            ],

            'status' => [
                'type' => 'toggle',
                'label' => 'Статус',
                'options' => [
                    0 => 'Неактивен',
                    1 => 'Активен',
                ]
            ],
        ];
    }

    /**
     * Получава стандартните настройки за куриера
     *
     * @return array
     */
    public static function getDefaultSettings()
    {
        return [
            'price' => 6.00,
            'fixed_price' => 6.00,
            'free_shipping_total' => 100.00,
            'sort_order' => 0,
            'status' => 1,
        ];
    }

    public function getSettings() {
        $this->loadModelAs('setting/delivery', 'deliveryModel');
        $delivery_method = $this->deliveryModel->getDeliveryMethod(self::CODE);

        F()->log->developer($delivery_method, __FILE__, __LINE__);
        return $delivery_method;
    }

    public function getFixedPrice() {
        $settings = $this->getSettings();
        return $settings['fixed_price'];
    }

    /**
     * Валидира настройките на услугата
     *
     * @param array $settings
     * @return array Масив с грешки (празен ако няма грешки)
     */
    public static function validateSettings($settings)
    {
        $errors = [];

        if (empty($settings['price'])) {
            $errors[] = 'Фиксирана цена за доставка е задължително поле';
        }
        return $errors;
    }

    /**
     * Няма тест за услугата
     *
     * @param array $settings
     * @return bool
     */
    public static function canTestConnection($settings)
    {
        return false;
    }

    function getQuote($address) {

		// $query = $this->db->query("SELECT * FROM " . DB_PREFIX . "zone_to_geo_zone WHERE geo_zone_id = '" . (int)$this->config->get('shipping_flat_geo_zone_id') . "' AND country_id = '" . (int)$address['country_id'] . "' AND (zone_id = '" . (int)$address['zone_id'] . "' OR zone_id = '0')");

		// if (!$this->config->get('shipping_flat_geo_zone_id')) {
		// 	$status = true;
		// } elseif ($query->num_rows) {
		// 	$status = true;
		// } else {
		// 	$status = false;
		// }

		$method_data = array();

		// if ($status) {
			$quote_data = array();

			$quote_data['flat'] = array(
				'code'         => 'flat.flat',
				'title'        => self::NAME,
				'cost'         => $this->getFixedPrice(),
				// 'tax_class_id' => $this->config->get('shipping_flat_tax_class_id'),
                'tax_class_id' => 0,
				'text'         => $this->currency->format($this->getFixedPrice())
			);

			$method_data = array(
				'code'       => 'flat',
				'title'      => self::NAME,
				'quote'      => $quote_data,
				'sort_order' => $this->getSettings()['sort_order'],
				'error'      => false
			);
		// }

		return $method_data;
	}

    public function __construct($registry) {
        $this->registry = $registry;
    }

    public function __get($key) {
        return $this->registry->get($key);
    }

    public function __set($key, $value) {
        $this->registry->set($key, $value);
    }

    public function __call($method, $args) {
        return call_user_func_array([CM($this->registry), $method], $args);
    }
}
